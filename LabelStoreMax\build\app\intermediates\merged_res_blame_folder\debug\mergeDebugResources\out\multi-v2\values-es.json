{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-108:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,148,209,271,352,416,491,552,633,708,776,838", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "143,204,266,347,411,486,547,628,703,771,833,905"}, "to": {"startLines": "206,216,218,219,220,224,232,235,238,242,246,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19068,19940,20085,20147,20228,20516,21051,21244,21501,21835,22187,22485", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "19156,19996,20142,20223,20287,20586,21107,21320,21571,21898,22244,22552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d75c5a854b2b1764f3caa76d8e9ef40e\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "340", "startColumns": "4", "startOffsets": "31925", "endColumns": "84", "endOffsets": "32005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,327,422,628,674,746,849,924,1165,1227,1320,1396,1453,1517,1591,1681,1978,2051,2118,2186,2245,2333,2468,2580,2638,2727,2809,2912,2982,3082,3403,3480,3558,3639,3714,3776,3837,3909,3995,4105,4211,4301,4410,4504,4580,4659,4745,4927,5132,5241,5367,5429,6200,6311,6376", "endColumns": "113,157,94,205,45,71,102,74,240,61,92,75,56,63,73,89,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,71,85,109,105,89,108,93,75,78,85,181,204,108,125,61,770,110,64,57", "endOffsets": "164,322,417,623,669,741,844,919,1160,1222,1315,1391,1448,1512,1586,1676,1973,2046,2113,2181,2240,2328,2463,2575,2633,2722,2804,2907,2977,3077,3398,3475,3553,3634,3709,3771,3832,3904,3990,4100,4206,4296,4405,4499,4575,4654,4740,4922,5127,5236,5362,5424,6195,6306,6371,6429"}, "to": {"startLines": "267,268,269,271,275,276,277,278,279,280,281,297,300,302,306,307,312,318,319,327,337,341,342,343,344,345,351,354,359,361,362,363,364,367,369,370,374,378,379,381,383,395,420,421,422,423,424,442,449,450,451,452,454,455,456,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24027,24141,24299,24488,25549,25595,25667,25770,25845,26086,26148,28205,28489,28680,28985,29059,29433,30108,30181,30752,31708,32010,32098,32233,32345,32403,32999,33269,33750,33901,34001,34322,34399,34645,34848,34923,35231,35809,35881,36066,36276,38046,40493,40602,40696,40772,40851,42566,43245,43450,43559,43685,43823,44594,44705,46517", "endColumns": "113,157,94,205,45,71,102,74,240,61,92,75,56,63,73,89,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,71,85,109,105,89,108,93,75,78,85,181,204,108,125,61,770,110,64,57", "endOffsets": "24136,24294,24389,24689,25590,25662,25765,25840,26081,26143,26236,28276,28541,28739,29054,29144,29725,30176,30243,30815,31762,32093,32228,32340,32398,32487,33076,33367,33815,33996,34317,34394,34472,34721,34918,34980,35287,35876,35962,36171,36377,38131,40597,40691,40767,40846,40932,42743,43445,43554,43680,43742,44589,44700,44765,46570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,276,352,437,497,556,614,708,789,883,996,1109,1197,1275,1359,1443,1544,1639,1711,1803,1891,1979,2087,2169,2261,2340,2439,2517,2615,2709,2800,2898,3016,3092,3184,3287,3383,3459,3527,4238,4929,5008,5138,5245,5300,5398,5490,5575,5685,5798,5857,5943,5994,6074,6184,6262,6335,6402,6468,6516,6597,6703,6776,6822,6872,6943,7001,7065,7265,7412,7557,7629,7715,7799,7894,7984,8082,8160,8253,8334,8434,8519,8616,8671,8791,8842,8904,8980,9050,9129,9199,9270,9364,9439,9492", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,95,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,66,65,47,80,105,72,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,99,84,96,54,119,50,61,75,69,78,69,70,93,74,52,71", "endOffsets": "125,209,271,347,432,492,551,609,703,784,878,991,1104,1192,1270,1354,1438,1539,1634,1706,1798,1886,1974,2082,2164,2256,2335,2434,2512,2610,2704,2795,2893,3011,3087,3179,3282,3378,3454,3522,4233,4924,5003,5133,5240,5295,5393,5485,5570,5680,5793,5852,5938,5989,6069,6179,6257,6330,6397,6463,6511,6592,6698,6771,6817,6867,6938,6996,7060,7260,7407,7552,7624,7710,7794,7889,7979,8077,8155,8248,8329,8429,8514,8611,8666,8786,8837,8899,8975,9045,9124,9194,9265,9359,9434,9487,9559"}, "to": {"startLines": "191,192,193,194,195,196,197,201,202,203,204,207,209,210,212,217,221,236,239,240,241,243,244,245,247,251,252,253,254,255,256,257,258,259,260,262,265,266,272,273,274,285,286,287,288,289,290,291,292,293,294,295,296,303,304,305,308,309,310,311,315,320,321,322,323,324,329,330,331,332,333,334,338,339,349,350,352,353,357,358,360,365,372,373,444,445,446,448,453,466,467,468,469,470,471,472,488", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17930,18005,18089,18151,18227,18312,18372,18674,18732,18826,18907,19161,19367,19480,19636,20001,20292,21325,21576,21671,21743,21903,21991,22079,22249,22557,22649,22728,22827,22905,23003,23097,23188,23286,23404,23557,23828,23931,24694,24770,24838,26500,27191,27270,27400,27507,27562,27660,27752,27837,27947,28060,28119,28744,28795,28875,29149,29227,29300,29367,29874,30248,30329,30435,30508,30554,30884,30955,31013,31077,31277,31424,31767,31839,32820,32904,33081,33171,33579,33657,33820,34477,35049,35134,42847,42902,43022,43183,43747,46005,46075,46154,46224,46295,46389,46464,47885", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,95,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,66,65,47,80,105,72,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,99,84,96,54,119,50,61,75,69,78,69,70,93,74,52,71", "endOffsets": "18000,18084,18146,18222,18307,18367,18426,18727,18821,18902,18996,19269,19475,19563,19709,20080,20371,21421,21666,21738,21830,21986,22074,22182,22326,22644,22723,22822,22900,22998,23092,23183,23281,23399,23475,23644,23926,24022,24765,24833,25544,27186,27265,27395,27502,27557,27655,27747,27832,27942,28055,28114,28200,28790,28870,28980,29222,29295,29362,29428,29917,30324,30430,30503,30549,30599,30950,31008,31072,31272,31419,31564,31834,31920,32899,32994,33166,33264,33652,33745,33896,34572,35129,35226,42897,43017,43068,43240,43818,46070,46149,46219,46290,46384,46459,46512,47952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8393", "endColumns": "159", "endOffsets": "8548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "509,510", "startColumns": "4,4", "startOffsets": "49942,50042", "endColumns": "99,101", "endOffsets": "50037,50139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7357,7465,7628,7759,7867,8028,8161,8283,8553,8745,8854,9019,9151,9316,9473,9540,9609", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "7460,7623,7754,7862,8023,8156,8278,8388,8740,8849,9014,9146,9311,9468,9535,9604,9689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,16893", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,16971"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,997,1067,1153,1244,1321,1403,1473", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,992,1062,1148,1239,1316,1398,1468,1589"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,506,507,508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4542,4638,9871,9969,10155,11103,11182,16083,16175,16343,16416,16716,16802,17135,49669,49751,49821", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "4633,4715,9964,10067,10239,11177,11270,16170,16257,16411,16481,16797,16888,17207,49746,49816,49937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3503,3602,3704,3804,3902,4009,4115,17289", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3597,3699,3799,3897,4004,4110,4230,17385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1091,1156,1251,1332,1395,1484,1548,1617,1680,1754,1818,1875,1993,2051,2113,2170,2250,2389,2478,2554,2649,2730,2812,2953,3034,3114,3265,3355,3435,3491,3547,3613,3692,3774,3845,3934,4008,4085,4155,4234,4334,4418,4502,4594,4694,4768,4849,4951,5004,5089,5156,5249,5338,5400,5464,5527,5595,5708,5815,5919,6020,6080,6140,6223,6306,6382", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "273,354,433,520,621,717,821,943,1024,1086,1151,1246,1327,1390,1479,1543,1612,1675,1749,1813,1870,1988,2046,2108,2165,2245,2384,2473,2549,2644,2725,2807,2948,3029,3109,3260,3350,3430,3486,3542,3608,3687,3769,3840,3929,4003,4080,4150,4229,4329,4413,4497,4589,4689,4763,4844,4946,4999,5084,5151,5244,5333,5395,5459,5522,5590,5703,5810,5914,6015,6075,6135,6218,6301,6377,6454"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,3407,4235,4339,4461,10244,10306,10545,11022,11275,11338,11427,11491,11560,11623,11697,11761,11818,11936,11994,12056,12113,12193,12332,12421,12497,12592,12673,12755,12896,12977,13057,13208,13298,13378,13434,13490,13556,13635,13717,13788,13877,13951,14028,14098,14177,14277,14361,14445,14537,14637,14711,14792,14894,14947,15032,15099,15192,15281,15343,15407,15470,15538,15651,15758,15862,15963,16023,16486,16976,17059,17212", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "323,3135,3214,3301,3402,3498,4334,4456,4537,10301,10366,10635,11098,11333,11422,11486,11555,11618,11692,11756,11813,11931,11989,12051,12108,12188,12327,12416,12492,12587,12668,12750,12891,12972,13052,13203,13293,13373,13429,13485,13551,13630,13712,13783,13872,13946,14023,14093,14172,14272,14356,14440,14532,14632,14706,14787,14889,14942,15027,15094,15187,15276,15338,15402,15465,15533,15646,15753,15857,15958,16018,16078,16564,17054,17130,17284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b04a7b60dccd9124e74d23c94f4b7089\\transformed\\jetified-facebook-login-18.0.3\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,222,365,518,633,718,807,883,974,1063,1177,1292,1381,1470,1578,1701,1783,1869,2058,2147,2250,2365,2481", "endColumns": "166,142,152,114,84,88,75,90,88,113,114,88,88,107,122,81,85,188,88,102,114,115,132", "endOffsets": "217,360,513,628,713,802,878,969,1058,1172,1287,1376,1465,1573,1696,1778,1864,2053,2142,2245,2360,2476,2609"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4798,4965,5108,5261,5376,5461,5550,5626,5717,5806,5920,6035,6124,6213,6321,6444,6526,6612,6801,6890,6993,7108,7224", "endColumns": "166,142,152,114,84,88,75,90,88,113,114,88,88,107,122,81,85,188,88,102,114,115,132", "endOffsets": "4960,5103,5256,5371,5456,5545,5621,5712,5801,5915,6030,6119,6208,6316,6439,6521,6607,6796,6885,6988,7103,7219,7352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13a52594bedbd674c7585af371c24fcc\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,246,312,379,445,527", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "137,241,307,374,440,522,590"}, "to": {"startLines": "184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "17390,17477,17581,17647,17714,17780,17862", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "17472,17576,17642,17709,17775,17857,17925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,215,283,376,448,509,582,649,711,779,850,910,971,1045,1109,1178,1241,1316,1389,1470,1547,1633,1726,1843,1934,1984,2044,2132,2196,2266,2335,2446,2535,2639", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "117,210,278,371,443,504,577,644,706,774,845,905,966,1040,1104,1173,1236,1311,1384,1465,1542,1628,1721,1838,1929,1979,2039,2127,2191,2261,2330,2441,2530,2634,2737"}, "to": {"startLines": "205,208,211,213,214,215,222,223,225,226,227,228,229,230,231,233,234,237,248,249,261,263,264,298,299,313,325,326,328,335,336,346,347,355,356", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19001,19274,19568,19714,19807,19879,20376,20449,20591,20653,20721,20792,20852,20913,20987,21112,21181,21426,22331,22404,23480,23649,23735,28281,28398,29730,30604,30664,30820,31569,31639,32492,32603,33372,33476", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "19063,19362,19631,19802,19874,19935,20444,20511,20648,20716,20787,20847,20908,20982,21046,21176,21239,21496,22399,22480,23552,23730,23823,28393,28484,29775,30659,30747,30879,31634,31703,32598,32687,33471,33574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9764,10640,10741,10856", "endColumns": "106,100,114,104", "endOffsets": "9866,10736,10851,10956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,216", "endColumns": "77,82,77", "endOffsets": "128,211,289"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4720,10072,10467", "endColumns": "77,82,77", "endOffsets": "4793,10150,10540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "106,513", "startColumns": "4,4", "startOffsets": "10961,50314", "endColumns": "60,77", "endOffsets": "11017,50387"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,219,298,392,484,568,651,785,879,973,1065,1193,1261,1383,1447,1619,1808,1964,2063,2163,2260,2360,2561,2666,2767,3038,3153,3296,3503,3729,3827,3954,4147,4241,4302,4368,4449,4555,4664,4746,4846,4940,5045,5263,5331,5402,5478,5603,5675,5759,5834,5928,5999,6067,6184,6272,6392,6491,6568,6663,6732,6820,6881,6987,7098,7198,7337,7406,7503,7607,7742,7813,7912,8022,8080,8136,8222,8320,8498,8780,9078,9176,9257,9347,9414,9531,9637,9722,9810,9883,9966,10055,10152,10280,10425,10496,10567,10620,10784,10849,10914,10995,11068,11148,11227,11372,11494,11604,11694,11782,11867,11943", "endColumns": "74,88,78,93,91,83,82,133,93,93,91,127,67,121,63,171,188,155,98,99,96,99,200,104,100,270,114,142,206,225,97,126,192,93,60,65,80,105,108,81,99,93,104,217,67,70,75,124,71,83,74,93,70,67,116,87,119,98,76,94,68,87,60,105,110,99,138,68,96,103,134,70,98,109,57,55,85,97,177,281,297,97,80,89,66,116,105,84,87,72,82,88,96,127,144,70,70,52,163,64,64,80,72,79,78,144,121,109,89,87,84,75,166", "endOffsets": "125,214,293,387,479,563,646,780,874,968,1060,1188,1256,1378,1442,1614,1803,1959,2058,2158,2255,2355,2556,2661,2762,3033,3148,3291,3498,3724,3822,3949,4142,4236,4297,4363,4444,4550,4659,4741,4841,4935,5040,5258,5326,5397,5473,5598,5670,5754,5829,5923,5994,6062,6179,6267,6387,6486,6563,6658,6727,6815,6876,6982,7093,7193,7332,7401,7498,7602,7737,7808,7907,8017,8075,8131,8217,8315,8493,8775,9073,9171,9252,9342,9409,9526,9632,9717,9805,9878,9961,10050,10147,10275,10420,10491,10562,10615,10779,10844,10909,10990,11063,11143,11222,11367,11489,11599,11689,11777,11862,11938,12105"}, "to": {"startLines": "198,199,200,270,282,283,284,301,314,316,317,348,366,368,371,375,376,377,380,382,384,385,386,387,388,389,390,391,392,393,394,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,443,447,457,458,459,460,461,462,463,464,465,474,475,476,477,478,479,480,481,482,483,484,485,486,487,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18431,18506,18595,24394,26241,26333,26417,28546,29780,29922,30016,32692,34577,34726,34985,35292,35464,35653,35967,36176,36382,36479,36579,36780,36885,36986,37257,37372,37515,37722,37948,38136,38263,38456,38550,38611,38677,38758,38864,38973,39055,39155,39249,39354,39572,39640,39711,39787,39912,39984,40068,40143,40237,40308,40376,40937,41025,41145,41244,41321,41416,41485,41573,41634,41740,41851,41951,42090,42159,42256,42360,42495,42748,43073,44770,44828,44884,44970,45068,45246,45528,45826,45924,46575,46665,46732,46849,46955,47040,47128,47201,47284,47373,47470,47598,47743,47814,47957,48010,48174,48239,48304,48385,48458,48538,48617,48762,48884,48994,49084,49172,49257,49333", "endColumns": "74,88,78,93,91,83,82,133,93,93,91,127,67,121,63,171,188,155,98,99,96,99,200,104,100,270,114,142,206,225,97,126,192,93,60,65,80,105,108,81,99,93,104,217,67,70,75,124,71,83,74,93,70,67,116,87,119,98,76,94,68,87,60,105,110,99,138,68,96,103,134,70,98,109,57,55,85,97,177,281,297,97,80,89,66,116,105,84,87,72,82,88,96,127,144,70,70,52,163,64,64,80,72,79,78,144,121,109,89,87,84,75,166", "endOffsets": "18501,18590,18669,24483,26328,26412,26495,28675,29869,30011,30103,32815,34640,34843,35044,35459,35648,35804,36061,36271,36474,36574,36775,36880,36981,37252,37367,37510,37717,37943,38041,38258,38451,38545,38606,38672,38753,38859,38968,39050,39150,39244,39349,39567,39635,39706,39782,39907,39979,40063,40138,40232,40303,40371,40488,41020,41140,41239,41316,41411,41480,41568,41629,41735,41846,41946,42085,42154,42251,42355,42490,42561,42842,43178,44823,44879,44965,45063,45241,45523,45821,45919,46000,46660,46727,46844,46950,47035,47123,47196,47279,47368,47465,47593,47738,47809,47880,48005,48169,48234,48299,48380,48453,48533,48612,48757,48879,48989,49079,49167,49252,49328,49495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,271,352,499,668,756", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "170,266,347,494,663,751,833"}, "to": {"startLines": "92,100,171,175,505,511,512", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9694,10371,16262,16569,49500,50144,50232", "endColumns": "69,95,80,146,168,87,81", "endOffsets": "9759,10462,16338,16711,49664,50227,50309"}}]}]}