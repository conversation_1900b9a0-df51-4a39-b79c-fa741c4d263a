{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-108:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ta\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "156", "endOffsets": "351"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8489", "endColumns": "160", "endOffsets": "8645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,264,347,496,665,746", "endColumns": "69,88,82,148,168,80,76", "endOffsets": "170,259,342,491,660,741,818"}, "to": {"startLines": "92,100,171,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9741,10411,16370,16698,17639,18268,18349", "endColumns": "69,88,82,148,168,80,76", "endOffsets": "9806,10495,16448,16842,17803,18344,18421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,139,213", "endColumns": "83,73,82", "endOffsets": "134,208,291"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4781,10123,10500", "endColumns": "83,73,82", "endOffsets": "4860,10192,10578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,435,537,652,741,852,973,1052,1128,1226,1326,1421,1515,1622,1722,1824,1918,2016,2114,2195,2303,2406,2505,2621,2724,2829,2986,17015", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "430,532,647,736,847,968,1047,1123,1221,1321,1416,1510,1617,1717,1819,1913,2011,2109,2190,2298,2401,2500,2616,2719,2824,2981,3083,17092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ta\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,667,815,939,1048,1145,1321,1424,1573,1704,1854,2006,2064,2123", "endColumns": "100,147,122,101,147,123,108,96,175,102,148,130,149,151,57,58,76", "endOffsets": "293,441,564,666,814,938,1047,1144,1320,1423,1572,1703,1853,2005,2063,2122,2199"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7505,7610,7762,7889,7995,8147,8275,8388,8650,8830,8937,9090,9225,9379,9535,9597,9660", "endColumns": "104,151,126,105,151,127,112,100,179,106,152,134,153,155,61,62,80", "endOffsets": "7605,7757,7884,7990,8142,8270,8383,8484,8825,8932,9085,9220,9374,9530,9592,9655,9736"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,148", "endColumns": "92,97", "endOffsets": "143,241"}, "to": {"startLines": "189,190", "startColumns": "4,4", "startOffsets": "18077,18170", "endColumns": "92,97", "endOffsets": "18165,18263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d75c5a854b2b1764f3caa76d8e9ef40e\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "184", "startColumns": "4", "startOffsets": "17545", "endColumns": "93", "endOffsets": "17634"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-ta\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "106,193", "startColumns": "4,4", "startOffsets": "11004,18426", "endColumns": "60,78", "endOffsets": "11060,18500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,360,446,530,633,727,836,954,1038,1097,1161,1269,1337,1398,1506,1573,1659,1717,1801,1868,1922,2045,2107,2170,2224,2312,2440,2526,2618,2721,2813,2895,3027,3107,3188,3344,3433,3517,3574,3626,3692,3777,3865,3936,4016,4085,4162,4242,4310,4425,4524,4607,4699,4793,4867,4953,5047,5097,5180,5246,5331,5418,5481,5546,5609,5678,5786,5884,5982,6079,6140,6196,6282,6374,6457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,85,83,102,93,108,117,83,58,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,155,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85,91,82,81", "endOffsets": "267,355,441,525,628,722,831,949,1033,1092,1156,1264,1332,1393,1501,1568,1654,1712,1796,1863,1917,2040,2102,2165,2219,2307,2435,2521,2613,2716,2808,2890,3022,3102,3183,3339,3428,3512,3569,3621,3687,3772,3860,3931,4011,4080,4157,4237,4305,4420,4519,4602,4694,4788,4862,4948,5042,5092,5175,5241,5326,5413,5476,5541,5604,5673,5781,5879,5977,6074,6135,6191,6277,6369,6452,6534"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3088,3176,3262,3346,3449,4289,4398,4516,10288,10347,10583,11065,11325,11386,11494,11561,11647,11705,11789,11856,11910,12033,12095,12158,12212,12300,12428,12514,12606,12709,12801,12883,13015,13095,13176,13332,13421,13505,13562,13614,13680,13765,13853,13924,14004,14073,14150,14230,14298,14413,14512,14595,14687,14781,14855,14941,15035,15085,15168,15234,15319,15406,15469,15534,15597,15666,15774,15872,15970,16067,16128,16612,17097,17189,17362", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,87,85,83,102,93,108,117,83,58,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,155,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85,91,82,81", "endOffsets": "317,3171,3257,3341,3444,3538,4393,4511,4595,10342,10406,10686,11128,11381,11489,11556,11642,11700,11784,11851,11905,12028,12090,12153,12207,12295,12423,12509,12601,12704,12796,12878,13010,13090,13171,13327,13416,13500,13557,13609,13675,13760,13848,13919,13999,14068,14145,14225,14293,14408,14507,14590,14682,14776,14850,14936,15030,15080,15163,15229,15314,15401,15464,15529,15592,15661,15769,15867,15965,16062,16123,16179,16693,17184,17267,17439"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3543,3639,3742,3841,3939,4046,4161,17444", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3634,3737,3836,3934,4041,4156,4284,17540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b04a7b60dccd9124e74d23c94f4b7089\\transformed\\jetified-facebook-login-18.0.3\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,378,511,622,710,808,886,979,1074,1189,1304,1396,1488,1598,1737,1820,1903,2093,2191,2295,2406,2524", "endColumns": "182,139,132,110,87,97,77,92,94,114,114,91,91,109,138,82,82,189,97,103,110,117,170", "endOffsets": "233,373,506,617,705,803,881,974,1069,1184,1299,1391,1483,1593,1732,1815,1898,2088,2186,2290,2401,2519,2690"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4865,5048,5188,5321,5432,5520,5618,5696,5789,5884,5999,6114,6206,6298,6408,6547,6630,6713,6903,7001,7105,7216,7334", "endColumns": "182,139,132,110,87,97,77,92,94,114,114,91,91,109,138,82,82,189,97,103,110,117,170", "endOffsets": "5043,5183,5316,5427,5515,5613,5691,5784,5879,5994,6109,6201,6293,6403,6542,6625,6708,6898,6996,7100,7211,7329,7500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1030,1109,1191,1277,1367,1447,1516", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1025,1104,1186,1272,1362,1442,1511,1631"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4600,4697,9928,10022,10197,11133,11216,16184,16275,16453,16533,16847,16929,17272,17808,17888,17957", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "4692,4776,10017,10118,10283,11211,11320,16270,16365,16528,16607,16924,17010,17357,17883,17952,18072"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,274,381", "endColumns": "116,101,106,103", "endOffsets": "167,269,376,480"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9811,10691,10793,10900", "endColumns": "116,101,106,103", "endOffsets": "9923,10788,10895,10999"}}]}]}