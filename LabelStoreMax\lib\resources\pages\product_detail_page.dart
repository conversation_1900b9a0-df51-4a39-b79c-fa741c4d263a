//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.



import 'package:flutter/material.dart';
import '/resources/widgets/wishlist_icon_widget.dart';
import '/app/controllers/product_detail_controller.dart';
import '/app/models/cart_line_item.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/velvete_ui.dart';
import '/resources/themes/styles/design_constants.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';

import '/app/models/woocommerce_wrappers/my_product_variation.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/services/woocommerce_service.dart';
import '/resources/widgets/cached_image_widget.dart';

class ProductDetailPage extends NyStatefulWidget<ProductDetailController> {
  static RouteView path = ("/product-detail", (_) => ProductDetailPage());

  ProductDetailPage({super.key}) : super(child: () => _ProductDetailState());
}

class _ProductDetailState extends NyPage<ProductDetailPage> {
  MyProduct? _product;

  List<MyProductVariation> _productVariations = [];
  final Map<int, dynamic> _tmpAttributeObj = {};
  final Map<String, String> _selectedAttributes = {}; // Track selected attributes
  // Remove WooSignalApp reference for now

  @override
  get init => () async {
        final data = widget.controller.data();
        WooCommerceService wooCommerceService = WooCommerceService();
        if (data is Map && data.containsKey("productId")) {
          _product = await wooCommerceService.getProduct(data["productId"]);
        } else {
          _product = data;
        }
        widget.controller.product = _product;
        if (_product?.type == "variable") {
          await _fetchProductVariations();
        }
      };

  @override
  LoadingStyle loadingStyle = LoadingStyle.skeletonizer();

  String _getAttributeName(int index) {
    if (_product?.attributes == null || index >= _product!.attributes.length) {
      return "";
    }

    final attribute = _product!.attributes[index];
    // Handle both MyProductAttribute objects and raw Map data
    if (attribute is Map<String, dynamic>) {
      return attribute['name']?.toString() ?? "";
    } else {
      // This should be a MyProductAttribute object with a name property
      try {
        return attribute.name?.toString() ?? "";
      } catch (e) {
        print('⚠️ Error accessing attribute name: $e');
        // Fallback: try to convert to string and extract name
        String attrStr = attribute.toString();
        return attrStr.isNotEmpty ? attrStr : "Unknown Attribute";
      }
    }
  }

  List<String> _getAttributeOptions(int index) {
    if (_product?.attributes == null || index >= _product!.attributes.length) {
      return [];
    }

    final attribute = _product!.attributes[index];
    if (attribute is Map<String, dynamic>) {
      final options = attribute['options'];
      if (options is List) {
        return options.map((option) => option.toString()).toList();
      }
    } else if (attribute != null) {
      // Try to access options property if it exists
      try {
        final options = attribute.options;
        if (options is List) {
          return options.map((option) => option.toString()).toList();
        }
      } catch (e) {
        // Fallback
      }
    }
    return [];
  }

  _fetchProductVariations() async {
    List<MyProductVariation> tmpVariations = [];
    int currentPage = 1;
    WooCommerceService wooCommerceService = WooCommerceService();

    bool isFetching = true;
    if (_product?.id == null) {
      return;
    }
    while (isFetching) {
      List<MyProductVariation> tmp = await wooCommerceService.getProductVariations(
        _product!.id,
        page: currentPage,
        perPage: 100,
      );
      if (tmp.isNotEmpty) {
        tmpVariations.addAll(tmp);
      }

      if (tmp.length >= 100) {
        currentPage += 1;
      } else {
        isFetching = false;
      }
    }
    _productVariations = tmpVariations;
  }

  _modalBottomSheetOptionsForAttribute(int attributeIndex) {
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: EdgeInsets.all(20),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "${trans("Select")} ${_getAttributeName(attributeIndex)}",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: Colors.grey[600]),
                ),
              ],
            ),
            Divider(color: Colors.grey[300]),
            SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _getAttributeOptions(attributeIndex).length,
                itemBuilder: (BuildContext context, int index) {
                  final options = _getAttributeOptions(attributeIndex);
                  final optionValue = options[index];
                  final isSelected = (_tmpAttributeObj.isNotEmpty &&
                      _tmpAttributeObj.containsKey(attributeIndex) &&
                      _tmpAttributeObj[attributeIndex]["value"] == optionValue);

                  return Container(
                    margin: EdgeInsets.symmetric(vertical: 4),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[300]!,
                        width: isSelected ? 2 : 1,
                      ),
                      boxShadow: isSelected ? [
                        BoxShadow(
                          color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () {
                          _tmpAttributeObj[attributeIndex] = {
                            "name": _getAttributeName(attributeIndex),
                            "value": optionValue
                          };
                          Navigator.pop(context);
                          _modalBottomSheetAttributes();
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  optionValue,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                    color: isSelected ? Colors.white : Colors.black87,
                                  ),
                                ),
                              ),
                              if (isSelected)
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.white,
                                  size: 24,
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  _modalBottomSheetAttributes() {
    MyProductVariation? productVariation = widget.controller
        .findProductVariation(
            tmpAttributeObj: _tmpAttributeObj,
            productVariations: _productVariations);
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: EdgeInsets.all(20),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  trans("Product Options"),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: Colors.grey[600]),
                ),
              ],
            ),
            Divider(color: Colors.grey[300]),
            SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _product?.attributes.length ?? 0,
                itemBuilder: (BuildContext context, int index) {
                  final isSelected = (_tmpAttributeObj.isNotEmpty &&
                      _tmpAttributeObj.containsKey(index));
                  final attributeName = _getAttributeName(index);

                  return Container(
                    margin: EdgeInsets.symmetric(vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[300]!,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () => _modalBottomSheetOptionsForAttribute(index),
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      attributeName,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      isSelected
                                          ? _tmpAttributeObj[index]["value"]
                                          : "${trans("Tap to select")} $attributeName",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: isSelected
                                            ? Theme.of(context).primaryColor
                                            : Colors.grey[600],
                                        fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                isSelected ? Icons.check_circle : Icons.arrow_forward_ios,
                                color: isSelected
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey[400],
                                size: isSelected ? 24 : 16,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            Container(
              decoration: BoxDecoration(
                  border: Border(top: BorderSide(color: Colors.black12, width: 1))),
              padding: EdgeInsets.only(top: 10),
              margin: EdgeInsets.only(bottom: 10),
              child: Column(
          children: <Widget>[
            Text(
              (productVariation != null
                  ? "${trans("Price")}: ${formatStringCurrency(total: productVariation.getSafePrice())}"
                  : (((_product?.attributes.length ==
                              _tmpAttributeObj.values.length) &&
                          productVariation == null)
                      ? trans("This variation is unavailable")
                      : trans("Choose your options"))),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              (productVariation != null
                  ? !productVariation.isInStock()
                      ? trans("Out of stock")
                      : ""
                  : ""),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            PrimaryButton(
                title: trans("Add to cart"),
                action: () async {
                  // DEFINITIVE ACTION 2: Fortify the "Add to Cart" Gates - ROBUST VALIDATION
                  if (_product?.id == null || _product?.id == 0) {
                    print('❌ DEFINITIVE ACTION 2: BLOCKED invalid product ID: ${_product?.id}');
                    showToast(
                      title: trans("Error"),
                      description: trans("Invalid product cannot be added to cart. Please try again."),
                      style: ToastNotificationStyleType.danger,
                    );
                    return; // IMMEDIATE STOP - Do not proceed to create CartLineItem
                  }

                  if (_product?.attributes.length !=
                      _tmpAttributeObj.values.length) {
                    showToast(
                        title: trans("Oops"),
                        description: trans("Please select valid options first"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  if (productVariation == null) {
                    showToast(
                        title: trans("Oops"),
                        description: trans("Product variation does not exist"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  if (!productVariation.isInStock()) {
                    showToast(
                        title: trans("Sorry"),
                        description: trans("This item is not in stock"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  List<String> options = [];
                  _tmpAttributeObj.forEach((k, v) {
                    options.add("${v["name"]}: ${v["value"]}");
                  });

                  print('🛒 Creating CartLineItem for variable product: ${_product!.name} (ID: ${_product!.id})');

                  CartLineItem cartLineItem = CartLineItem.fromProductVariation(
                    quantityAmount: widget.controller.quantity,
                    options: options,
                    product: _product!,
                    productVariation: productVariation,
                  );

                  print('✅ CartLineItem created with productId: ${cartLineItem.productId}');

                  await widget.controller.itemAddToCart(
                    cartLineItem: cartLineItem,
                  );
                  pop();
                }),
              ],
            ),
          ),
          ],
        ),
      ),
    );
  }

  @override
  Widget view(BuildContext context) {
    if (_product == null) {
      return Scaffold(
        body: Center(
          child: CircularProgressIndicator(
            color: const Color(0xFFB76E79),
          ),
        ),
      );
    }

    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverAppBar(
              backgroundColor: Theme.of(context).scaffoldBackgroundColor.withValues(
                alpha: innerBoxIsScrolled ? 0.95 : 1.0,
              ),
              elevation: innerBoxIsScrolled ? 4 : 0,
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Theme.of(context).iconTheme.color,
                ),
                onPressed: () => Navigator.pop(context),
              ),
              actions: [
                IconButton(
                  icon: Icon(
                    Icons.more_vert,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  onPressed: () {
                    _showMoreOptions(context);
                  },
                ),
                WishlistIcon(_product),
                IconButton(
                  icon: Icon(
                    Icons.close,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
              floating: true,
              snap: true,
              pinned: true,
              expandedHeight: 0,
            ),
          ];
        },
        body: Stack(
          children: [
            // Main content with enhanced scroll effects
            CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      _buildImageGallery(context),
                      _buildProductInfo(context),
                      _buildCollapsibleSections(context),
                      SizedBox(height: 100), // Space for bottom action bar
                    ],
                  ),
                ),
              ],
            ),
          // Sticky bottom action bar
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomActionBar(context),
          ),
        ],
      ),
      ),
    );
  }

  _addItemToCart() async {
    // DEFINITIVE ACTION 2: Fortify the "Add to Cart" Gates - ROBUST VALIDATION
    if (_product?.id == null || _product?.id == 0) {
      print('❌ DEFINITIVE ACTION 2: BLOCKED invalid product ID: ${_product?.id}');
      showToast(
        title: trans("Error"),
        description: trans("Invalid product cannot be added to cart. Please try again."),
        style: ToastNotificationStyleType.danger,
      );
      return; // IMMEDIATE STOP - Do not proceed to create CartLineItem
    }

    // Handle variable products with visible attribute selection
    if (_product?.type == "variable") {
      await _addVariableProductToCart();
      return;
    }

    // Handle simple products
    if (_product?.stockStatus != "instock") {
      showToast(
          title: trans("Sorry"),
          description: trans("This item is out of stock"),
          style: ToastNotificationStyleType.warning,
          icon: Icons.local_shipping);
      return;
    }

    print('🛒 Adding simple product to cart: ${_product!.name} (ID: ${_product!.id})');

    await widget.controller.itemAddToCart(
      cartLineItem: CartLineItem.fromProduct(
          quantityAmount: widget.controller.quantity, product: _product!),
    );
  }

  _addVariableProductToCart() async {
    // Check if all required attributes are selected
    if (_product?.attributes.isEmpty ?? true) {
      showToast(
        title: trans("Error"),
        description: trans("Product has no attributes"),
        style: ToastNotificationStyleType.danger,
      );
      return;
    }

    // Validate that all attributes are selected
    for (var attribute in _product!.attributes) {
      String attributeName = attribute.name ?? '';
      if (!_selectedAttributes.containsKey(attributeName) ||
          _selectedAttributes[attributeName]?.isEmpty == true) {
        showToast(
          title: trans("Oops"),
          description: trans("Please select all product options first"),
          style: ToastNotificationStyleType.warning,
        );
        return;
      }
    }

    // Convert selected attributes to the format expected by the variation finder
    Map<int, dynamic> attributeObj = {};
    for (int i = 0; i < _product!.attributes.length; i++) {
      String attributeName = _getAttributeName(i);
      if (_selectedAttributes.containsKey(attributeName)) {
        attributeObj[i] = {
          "name": attributeName,
          "value": _selectedAttributes[attributeName]
        };
      }
    }

    // Find the matching product variation
    MyProductVariation? productVariation = widget.controller.findProductVariation(
      tmpAttributeObj: attributeObj,
      productVariations: _productVariations,
    );

    if (productVariation == null) {
      showToast(
        title: trans("Oops"),
        description: trans("This variation is not available"),
        style: ToastNotificationStyleType.warning,
      );
      return;
    }

    if (!productVariation.isInStock()) {
      showToast(
        title: trans("Sorry"),
        description: trans("This item is not in stock"),
        style: ToastNotificationStyleType.warning,
      );
      return;
    }

    // Create options list for display
    List<String> options = [];
    attributeObj.forEach((k, v) {
      options.add("${v["name"]}: ${v["value"]}");
    });

    print('🛒 Adding variable product to cart: ${_product!.name} (ID: ${_product!.id}, Variation: ${productVariation.id})');

    CartLineItem cartLineItem = CartLineItem.fromProductVariation(
      quantityAmount: widget.controller.quantity,
      options: options,
      product: _product!,
      productVariation: productVariation,
    );

    await widget.controller.itemAddToCart(cartLineItem: cartLineItem);
  }



  Widget _buildImageGallery(BuildContext context) {
    List<String> images = [];

    // Add main image
    final productImages = _product?.images;
    if (productImages?.isNotEmpty == true) {
      images.addAll(productImages!.map((img) => img.src ?? '').where((src) => src.isNotEmpty));
    }

    // If no images, add placeholder
    if (images.isEmpty) {
      images.add(''); // Placeholder
    }

    return Container(
      height: 400,
      child: PageView.builder(
        itemCount: images.length,
        itemBuilder: (context, index) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey[800]
                  : Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
            ),
            child: images[index].isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Hero(
                      tag: index == 0 ? 'product_detail_image_${_product?.id}' : 'product_detail_image_${_product?.id}_$index',
                      child: CachedImageWidget(
                        image: images[index],
                        fit: BoxFit.cover,
                        height: double.infinity,
                        width: double.infinity,
                        placeholder: Center(
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            backgroundColor: Colors.black12,
                            color: Colors.black54,
                          ),
                        ),
                      ),
                    ),
                  )
                : _buildImagePlaceholder(),
          );
        },
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Icon(
          Icons.image,
          size: 80,
          color: Colors.grey[400],
        ),
      ),
    );
  }

  Widget _buildProductInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Name
          Text(
            _product?.name ?? '',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2E3A59),
                ),
            textAlign: TextAlign.right,
          ),

          SizedBox(height: 8),

          // SKU and Availability
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _product?.stockStatus == 'instock' ? 'متوفر' : 'غير متوفر',
                style: TextStyle(
                  color: _product?.stockStatus == 'instock'
                      ? Colors.green
                      : Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (_product?.sku?.isNotEmpty == true)
                Text(
                  'رمز المنتج: ${_product!.sku}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
            ],
          ),

          SizedBox(height: 16),

          // Price
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildQuantitySelector(),
              _buildPriceDisplay(),
            ],
          ),

          SizedBox(height: 20),

          // Product Attributes Selection (Prominent Display)
          _buildAttributeSelection(),
        ],
      ),
    );
  }

  Widget _buildPriceDisplay() {
    String currentPrice = '';
    if (_product?.onSale == true) {
      currentPrice = formatStringCurrency(total: _product?.salePrice);
    } else {
      currentPrice = formatStringCurrency(total: _product?.price);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          currentPrice,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: const Color(0xFFB76E79),
          ),
        ),
        if (_product?.onSale == true)
          Text(
            formatStringCurrency(total: _product?.regularPrice),
            style: TextStyle(
              fontSize: 16,
              decoration: TextDecoration.lineThrough,
              color: Colors.grey[600],
            ),
          ),
      ],
    );
  }

  Widget _buildAttributeSelection() {
    // Only show for variable products with attributes
    if (_product?.type != 'variable' || _product?.attributes == null || _product!.attributes.isEmpty) {
      return SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[800]
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[600]!
              : Colors.grey[200]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر المواصفات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2E3A59),
                ),
          ),
          SizedBox(height: 12),

          // Display each attribute with selection options
          ...(_product!.attributes).map((attr) {
            return _buildSingleAttributeSelector(attr);
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildSingleAttributeSelector(dynamic attr) {
    String attributeName = '';
    List<String> attributeOptions = [];

    try {
      // Handle dynamic attribute structure safely
      if (attr is Map<String, dynamic>) {
        attributeName = attr['name']?.toString() ?? '';

        // Handle options array
        if (attr['options'] is List) {
          attributeOptions = (attr['options'] as List)
              .map((option) => option.toString())
              .toList();
        }
      }
    } catch (e) {
      print('Error parsing attribute: $e');
      return SizedBox.shrink();
    }

    if (attributeName.isEmpty || attributeOptions.isEmpty) {
      return SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            attributeName,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF2E3A59),
                ),
          ),
          SizedBox(height: 8),

          // Options as chips
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: attributeOptions.map((option) {
              bool isSelected = _selectedAttributes[attributeName] == option;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedAttributes[attributeName] = option;
                  });
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFFB76E79)
                        : Colors.transparent,
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFFB76E79)
                          : Colors.grey[400]!,
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    option,
                    style: TextStyle(
                      color: isSelected
                          ? Colors.white
                          : const Color(0xFF2E3A59),
                      fontWeight: isSelected
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantitySelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: () => widget.controller.removeQuantityTapped(),
            icon: Icon(Icons.remove_circle_outline, color: const Color(0xFFB76E79)),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              '${widget.controller.quantity}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => widget.controller.addQuantityTapped(),
            icon: Icon(Icons.add_circle_outline, color: const Color(0xFFB76E79)),
          ),
        ],
      ),
    );
  }

  Widget _buildCollapsibleSections(BuildContext context) {
    return Column(
      children: [
        _buildCollapsibleSection(
          title: 'العلامة التجارية',
          content: _product?.name ?? 'غير محدد',
          icon: Icons.business,
        ),
        _buildCollapsibleSection(
          title: 'الوصف',
          content: _buildDescriptionWidget(),
          icon: Icons.description,
          isWidget: true,
        ),
        _buildCollapsibleSection(
          title: 'معلومات إضافية',
          content: _buildAdditionalInfo(),
          icon: Icons.info,
          isWidget: true,
        ),
      ],
    );
  }

  Widget _buildCollapsibleSection({
    required String title,
    required dynamic content,
    required IconData icon,
    bool isWidget = false,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Icon(
          icon,
          color: const Color(0xFFB76E79),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF2E3A59),
          ),
        ),
        children: [
          Padding(
            padding: EdgeInsets.all(16),
            child: isWidget
                ? content as Widget
                : Text(
                    content as String,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          height: 1.5,
                        ),
                    textAlign: TextAlign.right,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Handle attributes safely
        if (_product?.attributes != null && _product!.attributes.isNotEmpty)
          ...(_product!.attributes).map((attr) {
            String attributeName = '';
            String attributeOptions = '';

            try {
              // Handle dynamic attribute structure safely
              if (attr is Map<String, dynamic>) {
                attributeName = attr['name']?.toString() ?? '';
                final options = attr['options'];
                if (options is List) {
                  attributeOptions = options.map((opt) => opt?.toString() ?? '').join(', ');
                } else if (options != null) {
                  attributeOptions = options.toString();
                }
              } else {
                // Try to access as object with properties
                try {
                  attributeName = attr.name?.toString() ?? '';
                  final options = attr.options;
                  if (options is List) {
                    attributeOptions = options.map((opt) => opt?.toString() ?? '').join(', ');
                  } else if (options != null) {
                    attributeOptions = options.toString();
                  }
                } catch (e) {
                  // Fallback: convert entire attribute to string
                  attributeName = 'خاصية';
                  attributeOptions = attr.toString();
                }
              }
            } catch (e) {
              print('⚠️ Error processing attribute: $e');
              attributeName = 'خاصية';
              attributeOptions = 'غير محدد';
            }

            // Only show if we have meaningful data
            if (attributeName.isNotEmpty || attributeOptions.isNotEmpty) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        attributeOptions.isNotEmpty ? attributeOptions : 'غير محدد',
                        style: TextStyle(color: Colors.grey[600]),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      attributeName.isNotEmpty ? attributeName : 'خاصية',
                      style: TextStyle(fontWeight: FontWeight.w600),
                      textAlign: TextAlign.right,
                    ),
                  ],
                ),
              );
            } else {
              return SizedBox.shrink();
            }
          }).where((widget) => widget is! SizedBox),

        // Handle categories safely
        if (_product?.categories?.isNotEmpty == true)
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    _product!.categories!.map((cat) => cat.name).join(', '),
                    style: TextStyle(color: Colors.grey[600]),
                    textAlign: TextAlign.left,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  'الفئات',
                  style: TextStyle(fontWeight: FontWeight.w600),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),

        // Show SKU if available
        if (_product?.sku?.isNotEmpty == true)
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    _product!.sku!,
                    style: TextStyle(color: Colors.grey[600]),
                    textAlign: TextAlign.left,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  'رمز المنتج',
                  style: TextStyle(fontWeight: FontWeight.w600),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildBottomActionBar(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Buy Now Button (Secondary)
          Expanded(
            child: OutlinedButton(
              onPressed: () async {
                // First add item to cart, then navigate
                await _addItemToCart();
                // Navigate to cart after adding (only if mounted)
                if (mounted) {
                  Navigator.pushNamed(context, '/cart');
                }
              },
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: const Color(0xFFB76E79)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                'اشتر الآن',
                style: TextStyle(
                  color: const Color(0xFFB76E79),
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          SizedBox(width: 12),

          // Add to Cart Button (Primary) - Enhanced with animations
          Expanded(
            child: _AnimatedAddToCartButton(
              onPressed: _addItemToCart,
              text: 'أضف إلى السلة',
            ),
          ),
        ],
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.ios_share, color: const Color(0xFFB76E79)),
                title: Text('مشاركة المنتج'),
                onTap: () {
                  Navigator.pop(context);
                  // Implement share functionality
                },
              ),
              ListTile(
                leading: Icon(Icons.flag_outlined, color: const Color(0xFFB76E79)),
                title: Text('الإبلاغ عن مشكلة'),
                onTap: () {
                  Navigator.pop(context);
                  // Implement report functionality
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDescriptionWidget() {
    String description = _product?.description ?? '';

    if (description.isEmpty) {
      return Text(
        'لا يوجد وصف متاح',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              height: 1.5,
            ),
        textAlign: TextAlign.right,
      );
    }

    // Check if description contains HTML tags
    if (description.contains('<') && description.contains('>')) {
      return HtmlWidget(
        description,
        textStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
        customStylesBuilder: (element) {
          if (element.localName == 'p') {
            return {'text-align': 'right', 'direction': 'rtl'};
          }
          return null;
        },
      );
    } else {
      // Plain text description
      return Text(
        description,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
        textAlign: TextAlign.right,
      );
    }
  }
}

/// Enhanced Add to Cart Button with sophisticated micro-interactions and success feedback
class _AnimatedAddToCartButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final String text;

  const _AnimatedAddToCartButton({
    required this.onPressed,
    required this.text,
  });

  @override
  State<_AnimatedAddToCartButton> createState() => _AnimatedAddToCartButtonState();
}

class _AnimatedAddToCartButtonState extends State<_AnimatedAddToCartButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _successController;
  late AnimationController _pulseController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _successAnimation;
  late Animation<double> _pulseAnimation;

  bool _isProcessing = false;
  bool _showSuccess = false;

  @override
  void initState() {
    super.initState();

    // Scale animation for press feedback
    _scaleController = AnimationController(
      duration: DesignConstants.fastAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: DesignConstants.elegantCurve,
    ));

    // Success animation for add to cart feedback
    _successController = AnimationController(
      duration: DesignConstants.slowAnimation,
      vsync: this,
    );
    _successAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _successController,
      curve: DesignConstants.bouncyCurve,
    ));

    // Pulse animation for attention
    _pulseController = AnimationController(
      duration: Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _successController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _handleTap() async {
    if (widget.onPressed == null || _isProcessing) return;

    setState(() => _isProcessing = true);
    _scaleController.forward();

    // Execute the add to cart action
    widget.onPressed!();

    // Show success animation
    setState(() {
      _showSuccess = true;
      _isProcessing = false;
    });

    _successController.forward();
    _pulseController.repeat(reverse: true);

    // Reset after success animation
    await Future.delayed(Duration(milliseconds: 1500));

    if (mounted) {
      setState(() => _showSuccess = false);
      _successController.reset();
      _pulseController.stop();
      _scaleController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleAnimation,
        _successAnimation,
        _pulseAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value * (_showSuccess ? _pulseAnimation.value : 1.0),
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(DesignConstants.buttonRadius),
              gradient: _showSuccess
                  ? LinearGradient(
                      colors: [Colors.green.shade400, Colors.green.shade600],
                    )
                  : LinearGradient(
                      colors: [Color(0xFFB76E79), Color(0xFFA05C6B)],
                    ),
              boxShadow: DesignConstants.buttonShadow.map((shadow) {
                return shadow.copyWith(
                  color: _showSuccess
                      ? Colors.green.withValues(alpha: 0.3)
                      : shadow.color,
                );
              }).toList(),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(DesignConstants.buttonRadius),
                onTap: _handleTap,
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 16),
                  child: AnimatedSwitcher(
                    duration: DesignConstants.normalAnimation,
                    child: _showSuccess
                        ? Row(
                            key: ValueKey('success'),
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Transform.scale(
                                scale: _successAnimation.value,
                                child: Icon(
                                  Icons.check_circle,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                              SizedBox(width: 8),
                              Text(
                                'تمت الإضافة!',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          )
                        : _isProcessing
                            ? SizedBox(
                                key: ValueKey('loading'),
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : Text(
                                key: ValueKey('default'),
                                widget.text,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
