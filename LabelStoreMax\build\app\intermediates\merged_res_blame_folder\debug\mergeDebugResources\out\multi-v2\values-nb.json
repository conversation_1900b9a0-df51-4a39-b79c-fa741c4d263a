{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-108:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,215,294,387,473,551,628,749,842,941,1030,1150,1209,1330,1394,1571,1759,1899,1990,2093,2186,2287,2454,2562,2661,2891,2997,3123,3319,3533,3629,3742,3931,4025,4084,4148,4235,4332,4428,4505,4605,4694,4805,5029,5098,5165,5239,5346,5423,5512,5578,5659,5730,5806,5919,6005,6113,6203,6274,6362,6429,6515,6574,6676,6786,6891,7019,7080,7171,7267,7392,7463,7550,7651,7705,7763,7839,7945,8108,8377,8666,8762,8832,8919,8991,9097,9211,9298,9372,9441,9525,9609,9700,9821,9950,10023,10097,10149,10312,10381,10440,10514,10588,10666,10747,10888,11017,11131,11217,11292,11377,11445", "endColumns": "70,88,78,92,85,77,76,120,92,98,88,119,58,120,63,176,187,139,90,102,92,100,166,107,98,229,105,125,195,213,95,112,188,93,58,63,86,96,95,76,99,88,110,223,68,66,73,106,76,88,65,80,70,75,112,85,107,89,70,87,66,85,58,101,109,104,127,60,90,95,124,70,86,100,53,57,75,105,162,268,288,95,69,86,71,105,113,86,73,68,83,83,90,120,128,72,73,51,162,68,58,73,73,77,80,140,128,113,85,74,84,67,155", "endOffsets": "121,210,289,382,468,546,623,744,837,936,1025,1145,1204,1325,1389,1566,1754,1894,1985,2088,2181,2282,2449,2557,2656,2886,2992,3118,3314,3528,3624,3737,3926,4020,4079,4143,4230,4327,4423,4500,4600,4689,4800,5024,5093,5160,5234,5341,5418,5507,5573,5654,5725,5801,5914,6000,6108,6198,6269,6357,6424,6510,6569,6671,6781,6886,7014,7075,7166,7262,7387,7458,7545,7646,7700,7758,7834,7940,8103,8372,8661,8757,8827,8914,8986,9092,9206,9293,9367,9436,9520,9604,9695,9816,9945,10018,10092,10144,10307,10376,10435,10509,10583,10661,10742,10883,11012,11126,11212,11287,11372,11440,11596"}, "to": {"startLines": "190,191,192,262,274,275,276,293,306,308,309,339,357,359,362,366,367,368,371,373,375,376,377,378,379,380,381,382,383,384,385,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,434,438,448,449,450,451,452,453,454,455,456,465,466,467,468,469,470,471,472,473,474,475,476,477,478,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17028,17099,17188,22750,24527,24613,24691,26621,27825,27964,28063,30514,32237,32357,32600,32893,33070,33258,33549,33740,33936,34029,34130,34297,34405,34504,34734,34840,34966,35162,35376,35553,35666,35855,35949,36008,36072,36159,36256,36352,36429,36529,36618,36729,36953,37022,37089,37163,37270,37347,37436,37502,37583,37654,37730,38280,38366,38474,38564,38635,38723,38790,38876,38935,39037,39147,39252,39380,39441,39532,39628,39753,40001,40321,41864,41918,41976,42052,42158,42321,42590,42879,42975,43575,43662,43734,43840,43954,44041,44115,44184,44268,44352,44443,44564,44693,44766,44920,44972,45135,45204,45263,45337,45411,45489,45570,45711,45840,45954,46040,46115,46200,46268", "endColumns": "70,88,78,92,85,77,76,120,92,98,88,119,58,120,63,176,187,139,90,102,92,100,166,107,98,229,105,125,195,213,95,112,188,93,58,63,86,96,95,76,99,88,110,223,68,66,73,106,76,88,65,80,70,75,112,85,107,89,70,87,66,85,58,101,109,104,127,60,90,95,124,70,86,100,53,57,75,105,162,268,288,95,69,86,71,105,113,86,73,68,83,83,90,120,128,72,73,51,162,68,58,73,73,77,80,140,128,113,85,74,84,67,155", "endOffsets": "17094,17183,17262,22838,24608,24686,24763,26737,27913,28058,28147,30629,32291,32473,32659,33065,33253,33393,33635,33838,34024,34125,34292,34400,34499,34729,34835,34961,35157,35371,35467,35661,35850,35944,36003,36067,36154,36251,36347,36424,36524,36613,36724,36948,37017,37084,37158,37265,37342,37431,37497,37578,37649,37725,37838,38361,38469,38559,38630,38718,38785,38871,38930,39032,39142,39247,39375,39436,39527,39623,39748,39819,40083,40417,41913,41971,42047,42153,42316,42585,42874,42970,43040,43657,43729,43835,43949,44036,44110,44179,44263,44347,44438,44559,44688,44761,44835,44967,45130,45199,45258,45332,45406,45484,45565,45706,45835,45949,46035,46110,46195,46263,46419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,97,98,101,106,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,173,178,179,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,9792,9851,10075,10538,10770,10830,10917,10981,11043,11107,11175,11240,11294,11403,11461,11523,11577,11652,11772,11854,11931,12021,12105,12185,12319,12397,12477,12600,12688,12766,12820,12871,12937,13005,13079,13150,13226,13297,13375,13445,13515,13615,13704,13782,13870,13960,14032,14104,14188,14239,14317,14383,14464,14547,14609,14673,14736,14805,14905,15009,15102,15202,15260,15692,16154,16238,16386", "endLines": "5,33,34,35,36,37,45,46,47,97,98,101,106,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,173,178,179,181", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,9846,9910,10162,10601,10825,10912,10976,11038,11102,11170,11235,11289,11398,11456,11518,11572,11647,11767,11849,11926,12016,12100,12180,12314,12392,12472,12595,12683,12761,12815,12866,12932,13000,13074,13145,13221,13292,13370,13440,13510,13610,13699,13777,13865,13955,14027,14099,14183,14234,14312,14378,14459,14542,14604,14668,14731,14800,14900,15004,15097,15197,15255,15310,15765,16233,16311,16453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,262,330,407,465,524,577,653,730,811,910,1009,1095,1175,1253,1336,1433,1526,1595,1685,1772,1860,1969,2050,2140,2216,2313,2392,2488,2576,2665,2750,2851,2926,2999,3103,3200,3272,3337,4014,4666,4740,4855,4950,5005,5098,5183,5250,5338,5426,5483,5561,5610,5687,5793,5865,5940,6007,6073,6119,6198,6290,6362,6409,6457,6528,6586,6646,6826,6988,7112,7179,7263,7341,7438,7518,7600,7673,7762,7839,7941,8025,8110,8163,8295,8343,8397,8466,8532,8601,8665,8735,8823,8890,8941", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,96,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,66,65,45,78,91,71,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,101,83,84,52,131,47,53,68,65,68,63,69,87,66,50,79", "endOffsets": "118,195,257,325,402,460,519,572,648,725,806,905,1004,1090,1170,1248,1331,1428,1521,1590,1680,1767,1855,1964,2045,2135,2211,2308,2387,2483,2571,2660,2745,2846,2921,2994,3098,3195,3267,3332,4009,4661,4735,4850,4945,5000,5093,5178,5245,5333,5421,5478,5556,5605,5682,5788,5860,5935,6002,6068,6114,6193,6285,6357,6404,6452,6523,6581,6641,6821,6983,7107,7174,7258,7336,7433,7513,7595,7668,7757,7834,7936,8020,8105,8158,8290,8338,8392,8461,8527,8596,8660,8730,8818,8885,8936,9016"}, "to": {"startLines": "183,184,185,186,187,188,189,193,194,195,196,199,201,202,204,209,213,228,231,232,233,235,236,237,239,243,244,245,246,247,248,249,250,251,252,254,257,258,264,265,266,277,278,279,280,281,282,283,284,285,286,287,288,295,296,297,300,301,302,303,307,312,313,314,315,316,321,322,323,324,325,326,330,331,340,341,343,344,348,349,351,356,363,364,435,436,437,439,444,457,458,459,460,461,462,463,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16559,16627,16704,16766,16834,16911,16969,17267,17320,17396,17473,17697,17874,17973,18126,18475,18762,19779,20023,20116,20185,20341,20428,20516,20685,20988,21078,21154,21251,21330,21426,21514,21603,21688,21789,21938,22187,22291,23040,23112,23177,24768,25420,25494,25609,25704,25759,25852,25937,26004,26092,26180,26237,26806,26855,26932,27202,27274,27349,27416,27918,28292,28371,28463,28535,28582,28872,28943,29001,29061,29241,29403,29717,29784,30634,30712,30885,30965,31302,31375,31529,32135,32664,32748,40088,40141,40273,40422,40971,43045,43111,43180,43244,43314,43402,43469,44840", "endColumns": "67,76,61,67,76,57,58,52,75,76,80,98,98,85,79,77,82,96,92,68,89,86,87,108,80,89,75,96,78,95,87,88,84,100,74,72,103,96,71,64,676,651,73,114,94,54,92,84,66,87,87,56,77,48,76,105,71,74,66,65,45,78,91,71,46,47,70,57,59,179,161,123,66,83,77,96,79,81,72,88,76,101,83,84,52,131,47,53,68,65,68,63,69,87,66,50,79", "endOffsets": "16622,16699,16761,16829,16906,16964,17023,17315,17391,17468,17549,17791,17968,18054,18201,18548,18840,19871,20111,20180,20270,20423,20511,20620,20761,21073,21149,21246,21325,21421,21509,21598,21683,21784,21859,22006,22286,22383,23107,23172,23849,25415,25489,25604,25699,25754,25847,25932,25999,26087,26175,26232,26310,26850,26927,27033,27269,27344,27411,27477,27959,28366,28458,28530,28577,28625,28938,28996,29056,29236,29398,29522,29779,29863,30707,30804,30960,31042,31370,31459,31601,32232,32743,32828,40136,40268,40316,40471,41035,43106,43175,43239,43309,43397,43464,43515,44915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b04a7b60dccd9124e74d23c94f4b7089\\transformed\\jetified-facebook-login-18.0.3\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,362,501,612,693,779,860,949,1040,1153,1262,1353,1444,1545,1661,1741,1914,2011,2111,2224,2332", "endColumns": "161,144,138,110,80,85,80,88,90,112,108,90,90,100,115,79,172,96,99,112,107,136", "endOffsets": "212,357,496,607,688,774,855,944,1035,1148,1257,1348,1439,1540,1656,1736,1909,2006,2106,2219,2327,2464"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4623,4785,4930,5069,5180,5261,5347,5428,5517,5608,5721,5830,5921,6012,6113,6229,6309,6482,6579,6679,6792,6900", "endColumns": "161,144,138,110,80,85,80,88,90,112,108,90,90,100,115,79,172,96,99,112,107,136", "endOffsets": "4780,4925,5064,5175,5256,5342,5423,5512,5603,5716,5825,5916,6007,6108,6224,6304,6477,6574,6674,6787,6895,7032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7037,7143,7302,7428,7537,7693,7823,7943,8176,8330,8437,8598,8726,8868,9044,9111,9173", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "7138,7297,7423,7532,7688,7818,7938,8041,8325,8432,8593,8721,8863,9039,9106,9168,9246"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "500,501", "startColumns": "4,4", "startOffsets": "46857,46947", "endColumns": "89,87", "endOffsets": "46942,47030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "48,49,93,94,96,107,108,168,169,171,172,175,176,180,497,498,499", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4374,4467,9431,9528,9704,10606,10682,15315,15404,15564,15628,15912,15992,16316,46593,46670,46737", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "4462,4543,9523,9623,9787,10677,10765,15399,15481,15623,15687,15987,16069,16381,46665,46732,46852"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,182", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,16458", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,16554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "81", "startColumns": "4", "startOffsets": "8046", "endColumns": "129", "endOffsets": "8171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,16074", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,16149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "91,99,170,174,496,502,503", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9251,9915,15486,15770,46424,47035,47114", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "9316,9997,15559,15907,46588,47109,47185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,72", "endOffsets": "125,201,274"}, "to": {"startLines": "50,95,100", "startColumns": "4,4,4", "startOffsets": "4548,9628,10002", "endColumns": "74,75,72", "endOffsets": "4618,9699,10070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,322,417,614,662,729,826,895,1124,1189,1287,1353,1408,1472,1546,1636,1931,2005,2071,2124,2177,2269,2395,2502,2559,2643,2719,2802,2867,2966,3242,3319,3396,3457,3517,3579,3639,3710,3790,3890,3983,4064,4167,4264,4337,4416,4501,4678,4870,4988,5117,5173,5833,5932,5997", "endColumns": "112,153,94,196,47,66,96,68,228,64,97,65,54,63,73,89,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,70,79,99,92,80,102,96,72,78,84,176,191,117,128,55,659,98,64,54", "endOffsets": "163,317,412,609,657,724,821,890,1119,1184,1282,1348,1403,1467,1541,1631,1926,2000,2066,2119,2172,2264,2390,2497,2554,2638,2714,2797,2862,2961,3237,3314,3391,3452,3512,3574,3634,3705,3785,3885,3978,4059,4162,4259,4332,4411,4496,4673,4865,4983,5112,5168,5828,5927,5992,6047"}, "to": {"startLines": "259,260,261,263,267,268,269,270,271,272,273,289,292,294,298,299,304,310,311,319,329,332,333,334,335,336,342,345,350,352,353,354,355,358,360,361,365,369,370,372,374,386,411,412,413,414,415,433,440,441,442,443,445,446,447,464", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22388,22501,22655,22843,23854,23902,23969,24066,24135,24364,24429,26315,26566,26742,27038,27112,27482,28152,28226,28755,29664,29868,29960,30086,30193,30250,30809,31047,31464,31606,31705,31981,32058,32296,32478,32538,32833,33398,33469,33640,33843,35472,37843,37946,38043,38116,38195,39824,40476,40668,40786,40915,41040,41700,41799,43520", "endColumns": "112,153,94,196,47,66,96,68,228,64,97,65,54,63,73,89,294,73,65,52,52,91,125,106,56,83,75,82,64,98,275,76,76,60,59,61,59,70,79,99,92,80,102,96,72,78,84,176,191,117,128,55,659,98,64,54", "endOffsets": "22496,22650,22745,23035,23897,23964,24061,24130,24359,24424,24522,26376,26616,26801,27107,27197,27772,28221,28287,28803,29712,29955,30081,30188,30245,30329,30880,31125,31524,31700,31976,32053,32130,32352,32533,32595,32888,33464,33544,33735,33931,35548,37941,38038,38111,38190,38275,39996,40663,40781,40910,40966,41695,41794,41859,43570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,349,416,477,546,613,677,745,817,877,936,1009,1073,1143,1206,1281,1345,1434,1508,1593,1684,1782,1869,1917,1965,2042,2106,2173,2243,2337,2423,2511", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "115,193,260,344,411,472,541,608,672,740,812,872,931,1004,1068,1138,1201,1276,1340,1429,1503,1588,1679,1777,1864,1912,1960,2037,2101,2168,2238,2332,2418,2506,2590"}, "to": {"startLines": "197,200,203,205,206,207,214,215,217,218,219,220,221,222,223,225,226,229,240,241,253,255,256,290,291,305,317,318,320,327,328,337,338,346,347", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17554,17796,18059,18206,18290,18357,18845,18914,19051,19115,19183,19255,19315,19374,19447,19570,19640,19876,20766,20830,21864,22011,22096,26381,26479,27777,28630,28678,28808,29527,29594,30334,30428,31130,31218", "endColumns": "64,77,66,83,66,60,68,66,63,67,71,59,58,72,63,69,62,74,63,88,73,84,90,97,86,47,47,76,63,66,69,93,85,87,83", "endOffsets": "17614,17869,18121,18285,18352,18413,18909,18976,19110,19178,19250,19310,19369,19442,19506,19635,19698,19946,20825,20914,21933,22091,22182,26474,26561,27820,28673,28750,28867,29589,29659,30423,30509,31213,31297"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "92,102,103,104", "startColumns": "4,4,4,4", "startOffsets": "9321,10167,10268,10380", "endColumns": "109,100,111,96", "endOffsets": "9426,10263,10375,10472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,399,469,528,604,676,742,802", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "128,185,247,332,394,464,523,599,671,737,797,866"}, "to": {"startLines": "198,208,210,211,212,216,224,227,230,234,238,242", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17619,18418,18553,18615,18700,18981,19511,19703,19951,20275,20625,20919", "endColumns": "77,56,61,84,61,69,58,75,71,65,59,68", "endOffsets": "17692,18470,18610,18695,18757,19046,19565,19774,20018,20336,20680,20983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "105,504", "startColumns": "4,4", "startOffsets": "10477,47190", "endColumns": "60,74", "endOffsets": "10533,47260"}}]}]}