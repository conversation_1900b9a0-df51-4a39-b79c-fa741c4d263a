{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-108:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6720,6824,6958,7078,7184,7316,7436,7541,7762,7896,7997,8130,8249,8369,8489,8549,8608", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "6819,6953,7073,7179,7311,7431,7536,7635,7891,7992,8125,8244,8364,8484,8544,8603,8674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b04a7b60dccd9124e74d23c94f4b7089\\transformed\\jetified-facebook-login-18.0.3\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,195,308,436,545,632,717,792,880,967,1072,1173,1262,1351,1445,1553,1631,1711,1846,1941,2044,2151,2248", "endColumns": "139,112,127,108,86,84,74,87,86,104,100,88,88,93,107,77,79,134,94,102,106,96,101", "endOffsets": "190,303,431,540,627,712,787,875,962,1067,1168,1257,1346,1440,1548,1626,1706,1841,1936,2039,2146,2243,2345"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4425,4565,4678,4806,4915,5002,5087,5162,5250,5337,5442,5543,5632,5721,5815,5923,6001,6081,6216,6311,6414,6521,6618", "endColumns": "139,112,127,108,86,84,74,87,86,104,100,88,88,93,107,77,79,134,94,102,106,96,101", "endOffsets": "4560,4673,4801,4910,4997,5082,5157,5245,5332,5437,5538,5627,5716,5810,5918,5996,6076,6211,6306,6409,6516,6613,6715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,189,251,318,389,462,534,627,694,768,846,933,989,1078,1137,1277,1422,1530,1615,1697,1784,1871,1984,2071,2158,2295,2389,2493,2658,2829,2925,3019,3139,3218,3276,3337,3415,3495,3576,3643,3724,3803,3889,4028,4095,4159,4222,4318,4391,4468,4532,4604,4675,4743,4830,4911,5007,5089,5156,5234,5299,5371,5427,5514,5602,5690,5798,5857,5937,6027,6134,6202,6274,6357,6410,6464,6528,6609,6753,6956,7174,7260,7326,7396,7459,7545,7636,7715,7787,7857,7928,7998,8077,8160,8262,8333,8396,8447,8579,8642,8698,8767,8829,8901,8968,9081,9171,9252,9329,9397,9465,9526", "endColumns": "61,71,61,66,70,72,71,92,66,73,77,86,55,88,58,139,144,107,84,81,86,86,112,86,86,136,93,103,164,170,95,93,119,78,57,60,77,79,80,66,80,78,85,138,66,63,62,95,72,76,63,71,70,67,86,80,95,81,66,77,64,71,55,86,87,87,107,58,79,89,106,67,71,82,52,53,63,80,143,202,217,85,65,69,62,85,90,78,71,69,70,69,78,82,101,70,62,50,131,62,55,68,61,71,66,112,89,80,76,67,67,60,114", "endOffsets": "112,184,246,313,384,457,529,622,689,763,841,928,984,1073,1132,1272,1417,1525,1610,1692,1779,1866,1979,2066,2153,2290,2384,2488,2653,2824,2920,3014,3134,3213,3271,3332,3410,3490,3571,3638,3719,3798,3884,4023,4090,4154,4217,4313,4386,4463,4527,4599,4670,4738,4825,4906,5002,5084,5151,5229,5294,5366,5422,5509,5597,5685,5793,5852,5932,6022,6129,6197,6269,6352,6405,6459,6523,6604,6748,6951,7169,7255,7321,7391,7454,7540,7631,7710,7782,7852,7923,7993,8072,8155,8257,8328,8391,8442,8574,8637,8693,8762,8824,8896,8963,9076,9166,9247,9324,9392,9460,9521,9636"}, "to": {"startLines": "198,199,200,270,282,283,284,301,314,316,317,348,366,368,371,375,376,377,380,382,384,385,386,387,388,389,390,391,392,393,394,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,443,447,457,458,459,460,461,462,463,464,465,474,475,476,477,478,479,480,481,482,483,484,485,486,487,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16394,16456,16528,21542,22870,22941,23014,24592,25617,25729,25803,27961,29369,29480,29688,29960,30100,30245,30488,30663,30826,30913,31000,31113,31200,31287,31424,31518,31622,31787,31958,32133,32227,32347,32426,32484,32545,32623,32703,32784,32851,32932,33011,33097,33236,33303,33367,33430,33526,33599,33676,33740,33812,33883,33951,34470,34551,34647,34729,34796,34874,34939,35011,35067,35154,35242,35330,35438,35497,35577,35667,35774,35967,36227,37298,37351,37405,37469,37550,37694,37897,38115,38201,38756,38826,38889,38975,39066,39145,39217,39287,39358,39428,39507,39590,39692,39763,39888,39939,40071,40134,40190,40259,40321,40393,40460,40573,40663,40744,40821,40889,40957,41018", "endColumns": "61,71,61,66,70,72,71,92,66,73,77,86,55,88,58,139,144,107,84,81,86,86,112,86,86,136,93,103,164,170,95,93,119,78,57,60,77,79,80,66,80,78,85,138,66,63,62,95,72,76,63,71,70,67,86,80,95,81,66,77,64,71,55,86,87,87,107,58,79,89,106,67,71,82,52,53,63,80,143,202,217,85,65,69,62,85,90,78,71,69,70,69,78,82,101,70,62,50,131,62,55,68,61,71,66,112,89,80,76,67,67,60,114", "endOffsets": "16451,16523,16585,21604,22936,23009,23081,24680,25679,25798,25876,28043,29420,29564,29742,30095,30240,30348,30568,30740,30908,30995,31108,31195,31282,31419,31513,31617,31782,31953,32049,32222,32342,32421,32479,32540,32618,32698,32779,32846,32927,33006,33092,33231,33298,33362,33425,33521,33594,33671,33735,33807,33878,33946,34033,34546,34642,34724,34791,34869,34934,35006,35062,35149,35237,35325,35433,35492,35572,35662,35769,35837,36034,36305,37346,37400,37464,37545,37689,37892,38110,38196,38262,38821,38884,38970,39061,39140,39212,39282,39353,39423,39502,39585,39687,39758,39821,39934,40066,40129,40185,40254,40316,40388,40455,40568,40658,40739,40816,40884,40952,41013,41128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,506,507,508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4186,4272,8839,8928,9096,9952,10030,14271,14356,14501,14565,14830,14904,15210,41301,41377,41442", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "4267,4345,8923,9020,9174,10025,10103,14351,14426,14560,14624,14899,14975,15274,41372,41437,41554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,64", "endOffsets": "258,323"}, "to": {"startLines": "106,513", "startColumns": "4,4", "startOffsets": "9827,41882", "endColumns": "60,68", "endOffsets": "9883,41946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,189,256,318,388,442,502,555,626,692,762,848,934,1009,1093,1167,1240,1322,1403,1466,1544,1622,1697,1781,1856,1934,2004,2089,2158,2237,2310,2380,2455,2536,2600,2672,2767,2852,2914,2975,3433,3864,3934,4025,4114,4169,4253,4331,4392,4467,4547,4602,4676,4724,4795,4881,4947,5013,5080,5146,5191,5259,5343,5414,5457,5500,5569,5627,5686,5797,5908,6000,6063,6133,6197,6275,6343,6410,6473,6548,6612,6692,6768,6847,6896,6990,7035,7090,7152,7211,7279,7338,7401,7479,7539,7586", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,84,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,66,65,44,67,83,70,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,79,75,78,48,93,44,54,61,58,67,58,62,77,59,46,61", "endOffsets": "113,184,251,313,383,437,497,550,621,687,757,843,929,1004,1088,1162,1235,1317,1398,1461,1539,1617,1692,1776,1851,1929,1999,2084,2153,2232,2305,2375,2450,2531,2595,2667,2762,2847,2909,2970,3428,3859,3929,4020,4109,4164,4248,4326,4387,4462,4542,4597,4671,4719,4790,4876,4942,5008,5075,5141,5186,5254,5338,5409,5452,5495,5564,5622,5681,5792,5903,5995,6058,6128,6192,6270,6338,6405,6468,6543,6607,6687,6763,6842,6891,6985,7030,7085,7147,7206,7274,7333,7396,7474,7534,7581,7643"}, "to": {"startLines": "191,192,193,194,195,196,197,201,202,203,204,207,209,210,212,217,221,236,239,240,241,243,244,245,247,251,252,253,254,255,256,257,258,259,260,262,265,266,272,273,274,285,286,287,288,289,290,291,292,293,294,295,296,303,304,305,308,309,310,311,315,320,321,322,323,324,329,330,331,332,333,334,338,339,349,350,352,353,357,358,360,365,372,373,444,445,446,448,453,466,467,468,469,470,471,472,488", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15947,16010,16081,16148,16210,16280,16334,16590,16643,16714,16780,16983,17142,17228,17367,17707,17972,18952,19166,19247,19310,19451,19529,19604,19748,20022,20100,20170,20255,20324,20403,20476,20546,20621,20702,20839,21049,21144,21744,21806,21867,23086,23517,23587,23678,23767,23822,23906,23984,24045,24120,24200,24255,24749,24797,24868,25112,25178,25244,25311,25684,26003,26071,26155,26226,26269,26542,26611,26669,26728,26839,26950,27217,27280,28048,28112,28258,28326,28612,28675,28813,29289,29747,29823,36039,36088,36182,36310,36749,38267,38326,38394,38453,38516,38594,38654,39826", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,84,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,66,65,44,67,83,70,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,79,75,78,48,93,44,54,61,58,67,58,62,77,59,46,61", "endOffsets": "16005,16076,16143,16205,16275,16329,16389,16638,16709,16775,16845,17064,17223,17298,17446,17776,18040,19029,19242,19305,19383,19524,19599,19683,19818,20095,20165,20250,20319,20398,20471,20541,20616,20697,20761,20906,21139,21224,21801,21862,22320,23512,23582,23673,23762,23817,23901,23979,24040,24115,24195,24250,24324,24792,24863,24949,25173,25239,25306,25372,25724,26066,26150,26221,26264,26307,26606,26664,26723,26834,26945,27037,27275,27345,28107,28185,28321,28388,28670,28745,28872,29364,29818,29897,36083,36177,36222,36360,36806,38321,38389,38448,38511,38589,38649,38696,39883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,81", "endOffsets": "135,217"}, "to": {"startLines": "509,510", "startColumns": "4,4", "startOffsets": "41559,41644", "endColumns": "84,81", "endOffsets": "41639,41721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,184,243,317,375,437,494,561,627,690,750", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "123,179,238,312,370,432,489,556,622,685,745,808"}, "to": {"startLines": "206,216,218,219,220,224,232,235,238,242,246,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16910,17651,17781,17840,17914,18168,18697,18885,19100,19388,19688,19959", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "16978,17702,17835,17909,17967,18225,18749,18947,19161,19446,19743,20017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d75c5a854b2b1764f3caa76d8e9ef40e\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "73", "endOffsets": "124"}, "to": {"startLines": "340", "startColumns": "4", "startOffsets": "27350", "endColumns": "73", "endOffsets": "27419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3252,3344,3444,3538,3634,3727,3820,15350", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3339,3439,3533,3629,3722,3815,3916,15446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,934,997,1081,1145,1203,1284,1345,1409,1464,1523,1580,1634,1727,1783,1840,1894,1960,2060,2136,2207,2286,2359,2440,2562,2624,2686,2787,2866,2941,2994,3045,3111,3181,3251,3322,3392,3456,3527,3595,3658,3749,3828,3891,3971,4053,4125,4196,4268,4316,4388,4452,4527,4604,4666,4730,4793,4860,4946,5032,5113,5196,5253,5308,5381,5459,5532", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "248,315,379,448,529,611,696,800,876,929,992,1076,1140,1198,1279,1340,1404,1459,1518,1575,1629,1722,1778,1835,1889,1955,2055,2131,2202,2281,2354,2435,2557,2619,2681,2782,2861,2936,2989,3040,3106,3176,3246,3317,3387,3451,3522,3590,3653,3744,3823,3886,3966,4048,4120,4191,4263,4311,4383,4447,4522,4599,4661,4725,4788,4855,4941,5027,5108,5191,5248,5303,5376,5454,5527,5598"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2889,2956,3020,3089,3170,3921,4006,4110,9179,9232,9452,9888,10108,10166,10247,10308,10372,10427,10486,10543,10597,10690,10746,10803,10857,10923,11023,11099,11170,11249,11322,11403,11525,11587,11649,11750,11829,11904,11957,12008,12074,12144,12214,12285,12355,12419,12490,12558,12621,12712,12791,12854,12934,13016,13088,13159,13231,13279,13351,13415,13490,13567,13629,13693,13756,13823,13909,13995,14076,14159,14216,14629,15059,15137,15279", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "298,2951,3015,3084,3165,3247,4001,4105,4181,9227,9290,9531,9947,10161,10242,10303,10367,10422,10481,10538,10592,10685,10741,10798,10852,10918,11018,11094,11165,11244,11317,11398,11520,11582,11644,11745,11824,11899,11952,12003,12069,12139,12209,12280,12350,12414,12485,12553,12616,12707,12786,12849,12929,13011,13083,13154,13226,13274,13346,13410,13485,13562,13624,13688,13751,13818,13904,13990,14071,14154,14211,14266,14697,15132,15205,15345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ja\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "7640", "endColumns": "121", "endOffsets": "7757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,286,368,503,546,606,686,750,911,968,1048,1105,1159,1223,1294,1381,1574,1635,1696,1749,1800,1871,1973,2060,2114,2191,2259,2333,2396,2472,2664,2734,2808,2863,2921,2982,3040,3104,3175,3265,3346,3425,3527,3616,3695,3773,3857,3982,4116,4215,4312,4366,4713,4796,4853", "endColumns": "86,143,81,134,42,59,79,63,160,56,79,56,53,63,70,86,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,63,70,89,80,78,101,88,78,77,83,124,133,98,96,53,346,82,56,54", "endOffsets": "137,281,363,498,541,601,681,745,906,963,1043,1100,1154,1218,1289,1376,1569,1630,1691,1744,1795,1866,1968,2055,2109,2186,2254,2328,2391,2467,2659,2729,2803,2858,2916,2977,3035,3099,3170,3260,3341,3420,3522,3611,3690,3768,3852,3977,4111,4210,4307,4361,4708,4791,4848,4903"}, "to": {"startLines": "267,268,269,271,275,276,277,278,279,280,281,297,300,302,306,307,312,318,319,327,337,341,342,343,344,345,351,354,359,361,362,363,364,367,369,370,374,378,379,381,383,395,420,421,422,423,424,442,449,450,451,452,454,455,456,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21229,21316,21460,21609,22325,22368,22428,22508,22572,22733,22790,24329,24538,24685,24954,25025,25377,25881,25942,26427,27166,27424,27495,27597,27684,27738,28190,28393,28750,28877,28953,29145,29215,29425,29569,29627,29902,30353,30417,30573,30745,32054,34038,34140,34229,34308,34386,35842,36365,36499,36598,36695,36811,37158,37241,38701", "endColumns": "86,143,81,134,42,59,79,63,160,56,79,56,53,63,70,86,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,63,70,89,80,78,101,88,78,77,83,124,133,98,96,53,346,82,56,54", "endOffsets": "21311,21455,21537,21739,22363,22423,22503,22567,22728,22785,22865,24381,24587,24744,25020,25107,25565,25937,25998,26475,27212,27490,27592,27679,27733,27810,28253,28462,28808,28948,29140,29210,29284,29475,29622,29683,29955,30412,30483,30658,30821,32128,34135,34224,34303,34381,34465,35962,36494,36593,36690,36744,37153,37236,37293,38751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,201", "endColumns": "74,70,73", "endOffsets": "125,196,270"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4350,9025,9378", "endColumns": "74,70,73", "endOffsets": "4420,9091,9447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,255,325,453,621,701", "endColumns": "66,82,69,127,167,79,75", "endOffsets": "167,250,320,448,616,696,772"}, "to": {"startLines": "92,100,171,175,505,511,512", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8679,9295,14431,14702,41133,41726,41806", "endColumns": "66,82,69,127,167,79,75", "endOffsets": "8741,9373,14496,14825,41296,41801,41877"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "8746,9536,9631,9732", "endColumns": "92,94,100,94", "endOffsets": "8834,9626,9727,9822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,14980", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,15054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13a52594bedbd674c7585af371c24fcc\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,134,223,290,357,420,490", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "129,218,285,352,415,485,546"}, "to": {"startLines": "184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "15451,15530,15619,15686,15753,15816,15886", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "15525,15614,15681,15748,15811,15881,15942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,188,252,327,391,452,514,575,635,709,783,850,910,978,1042,1109,1173,1239,1298,1375,1448,1514,1586,1661,1738,1785,1834,1900,1962,2020,2086,2165,2232,2305", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "110,183,247,322,386,447,509,570,630,704,778,845,905,973,1037,1104,1168,1234,1293,1370,1443,1509,1581,1656,1733,1780,1829,1895,1957,2015,2081,2160,2227,2300,2372"}, "to": {"startLines": "205,208,211,213,214,215,222,223,225,226,227,228,229,230,231,233,234,237,248,249,261,263,264,298,299,313,325,326,328,335,336,346,347,355,356", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16850,17069,17303,17451,17526,17590,18045,18107,18230,18290,18364,18438,18505,18565,18633,18754,18821,19034,19823,19882,20766,20911,20977,24386,24461,25570,26312,26361,26480,27042,27100,27815,27894,28467,28540", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "16905,17137,17362,17521,17585,17646,18102,18163,18285,18359,18433,18500,18560,18628,18692,18816,18880,19095,19877,19954,20834,20972,21044,24456,24533,25612,26356,26422,26537,27095,27161,27889,27956,28535,28607"}}]}]}