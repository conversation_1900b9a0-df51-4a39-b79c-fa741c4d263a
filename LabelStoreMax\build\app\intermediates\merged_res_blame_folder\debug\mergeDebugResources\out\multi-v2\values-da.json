{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-108:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,490,659,739", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "170,258,337,485,654,734,811"}, "to": {"startLines": "91,99,170,174,504,510,511", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9295,9952,15562,15851,47273,47884,47964", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "9360,10035,15636,15994,47437,47959,48036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "92,102,103,104", "startColumns": "4,4,4,4", "startOffsets": "9365,10201,10300,10407", "endColumns": "111,98,106,96", "endOffsets": "9472,10295,10402,10499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,69", "endOffsets": "258,328"}, "to": {"startLines": "105,512", "startColumns": "4,4", "startOffsets": "10504,48041", "endColumns": "60,73", "endOffsets": "10560,48110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,182", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3383,3479,3581,3678,3776,3883,3992,16549", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3474,3576,3673,3771,3878,3987,4105,16645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,74", "endOffsets": "125,201,276"}, "to": {"startLines": "50,95,100", "startColumns": "4,4,4", "startOffsets": "4609,9671,10040", "endColumns": "74,75,74", "endOffsets": "4679,9742,10110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,16164", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,16239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13a52594bedbd674c7585af371c24fcc\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,248,315,382,448,521", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "139,243,310,377,443,516,583"}, "to": {"startLines": "183,184,185,186,187,188,189", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "16650,16739,16843,16910,16977,17043,17116", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "16734,16838,16905,16972,17038,17111,17178"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,353,420,481,550,617,681,749,817,877,935,1008,1072,1142,1205,1278,1342,1431,1505,1582,1673,1782,1867,1915,1970,2045,2107,2176,2245,2342,2429,2517", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "115,193,260,348,415,476,545,612,676,744,812,872,930,1003,1067,1137,1200,1273,1337,1426,1500,1577,1668,1777,1862,1910,1965,2040,2102,2171,2240,2337,2424,2512,2599"}, "to": {"startLines": "204,207,210,212,213,214,221,222,224,225,226,227,228,229,230,232,233,236,247,248,260,262,263,297,298,312,324,325,327,334,335,345,346,354,355", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18169,18410,18672,18818,18906,18973,19458,19527,19664,19728,19796,19864,19924,19982,20055,20178,20248,20477,21343,21407,22430,22579,22656,26996,27105,28424,29276,29331,29459,30173,30242,31074,31171,31888,31976", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "18229,18483,18734,18901,18968,19029,19522,19589,19723,19791,19859,19919,19977,20050,20114,20243,20306,20545,21402,21491,22499,22651,22742,27100,27185,28467,29326,29401,29516,30237,30306,31166,31253,31971,32058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "81", "startColumns": "4", "startOffsets": "8088", "endColumns": "126", "endOffsets": "8210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d75c5a854b2b1764f3caa76d8e9ef40e\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "339", "startColumns": "4", "startOffsets": "30512", "endColumns": "81", "endOffsets": "30589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "508,509", "startColumns": "4,4", "startOffsets": "47707,47797", "endColumns": "89,86", "endOffsets": "47792,47879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,260,328,403,463,522,575,647,727,805,903,1001,1087,1166,1243,1326,1419,1508,1574,1660,1746,1830,1934,2014,2101,2183,2285,2359,2447,2543,2629,2710,2809,2881,2956,3060,3157,3227,3292,3967,4618,4692,4809,4909,4964,5062,5152,5220,5311,5397,5454,5537,5588,5665,5761,5831,5902,5969,6035,6080,6158,6250,6326,6373,6421,6489,6547,6612,6794,6957,7073,7137,7221,7298,7399,7488,7572,7648,7740,7820,7922,8004,8090,8143,8274,8322,8376,8443,8510,8584,8648,8720,8808,8874,8924", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,96,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,66,65,44,77,91,75,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,101,81,85,52,130,47,53,66,66,73,63,71,87,65,49,75", "endOffsets": "118,195,255,323,398,458,517,570,642,722,800,898,996,1082,1161,1238,1321,1414,1503,1569,1655,1741,1825,1929,2009,2096,2178,2280,2354,2442,2538,2624,2705,2804,2876,2951,3055,3152,3222,3287,3962,4613,4687,4804,4904,4959,5057,5147,5215,5306,5392,5449,5532,5583,5660,5756,5826,5897,5964,6030,6075,6153,6245,6321,6368,6416,6484,6542,6607,6789,6952,7068,7132,7216,7293,7394,7483,7567,7643,7735,7815,7917,7999,8085,8138,8269,8317,8371,8438,8505,8579,8643,8715,8803,8869,8919,8995"}, "to": {"startLines": "190,191,192,193,194,195,196,200,201,202,203,206,208,209,211,216,220,235,238,239,240,242,243,244,246,250,251,252,253,254,255,256,257,258,259,261,264,265,271,272,273,284,285,286,287,288,289,290,291,292,293,294,295,302,303,304,307,308,309,310,314,319,320,321,322,323,328,329,330,331,332,333,337,338,348,349,351,352,356,357,359,364,371,372,443,444,445,447,452,465,466,467,468,469,470,471,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17183,17251,17328,17388,17456,17531,17591,17886,17939,18011,18091,18312,18488,18586,18739,19091,19375,20384,20622,20711,20777,20929,21015,21099,21263,21563,21650,21732,21834,21908,21996,22092,22178,22259,22358,22504,22747,22851,23613,23683,23748,25355,26006,26080,26197,26297,26352,26450,26540,26608,26699,26785,26842,27438,27489,27566,27832,27902,27973,28040,28564,28935,29013,29105,29181,29228,29521,29589,29647,29712,29894,30057,30364,30428,31375,31452,31632,31721,32063,32139,32297,32928,33460,33542,40896,40949,41080,41226,41774,43904,43971,44045,44109,44181,44269,44335,45699", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,96,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,66,65,44,77,91,75,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,101,81,85,52,130,47,53,66,66,73,63,71,87,65,49,75", "endOffsets": "17246,17323,17383,17451,17526,17586,17645,17934,18006,18086,18164,18405,18581,18667,18813,19163,19453,20472,20706,20772,20858,21010,21094,21198,21338,21645,21727,21829,21903,21991,22087,22173,22254,22353,22425,22574,22846,22943,23678,23743,24418,26001,26075,26192,26292,26347,26445,26535,26603,26694,26780,26837,26920,27484,27561,27657,27897,27968,28035,28101,28604,29008,29100,29176,29223,29271,29584,29642,29707,29889,30052,30168,30423,30507,31447,31548,31716,31800,32134,32226,32372,33025,33537,33623,40944,41075,41123,41275,41836,43966,44040,44104,44176,44264,44330,44380,45770"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,211,291,383,470,548,625,754,846,944,1033,1150,1208,1327,1391,1569,1760,1898,1987,2093,2184,2285,2466,2580,2678,2927,3035,3160,3352,3562,3658,3770,3958,4052,4110,4174,4255,4350,4451,4528,4628,4717,4827,5040,5109,5175,5249,5354,5431,5520,5586,5667,5738,5806,5914,6001,6111,6201,6272,6360,6427,6513,6570,6670,6776,6879,7005,7064,7155,7255,7376,7447,7534,7632,7686,7742,7816,7918,8087,8352,8642,8744,8815,8905,8976,9081,9196,9287,9361,9430,9515,9599,9687,9803,9937,10004,10074,10128,10298,10367,10426,10500,10574,10651,10732,10868,10992,11105,11188,11263,11348,11415", "endColumns": "68,86,79,91,86,77,76,128,91,97,88,116,57,118,63,177,190,137,88,105,90,100,180,113,97,248,107,124,191,209,95,111,187,93,57,63,80,94,100,76,99,88,109,212,68,65,73,104,76,88,65,80,70,67,107,86,109,89,70,87,66,85,56,99,105,102,125,58,90,99,120,70,86,97,53,55,73,101,168,264,289,101,70,89,70,104,114,90,73,68,84,83,87,115,133,66,69,53,169,68,58,73,73,76,80,135,123,112,82,74,84,66,156", "endOffsets": "119,206,286,378,465,543,620,749,841,939,1028,1145,1203,1322,1386,1564,1755,1893,1982,2088,2179,2280,2461,2575,2673,2922,3030,3155,3347,3557,3653,3765,3953,4047,4105,4169,4250,4345,4446,4523,4623,4712,4822,5035,5104,5170,5244,5349,5426,5515,5581,5662,5733,5801,5909,5996,6106,6196,6267,6355,6422,6508,6565,6665,6771,6874,7000,7059,7150,7250,7371,7442,7529,7627,7681,7737,7811,7913,8082,8347,8637,8739,8810,8900,8971,9076,9191,9282,9356,9425,9510,9594,9682,9798,9932,9999,10069,10123,10293,10362,10421,10495,10569,10646,10727,10863,10987,11100,11183,11258,11343,11410,11567"}, "to": {"startLines": "197,198,199,269,281,282,283,300,313,315,316,347,365,367,370,374,375,376,379,381,383,384,385,386,387,388,389,390,391,392,393,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,442,446,456,457,458,459,460,461,462,463,464,473,474,475,476,477,478,479,480,481,482,483,484,485,486,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17650,17719,17806,23312,25113,25200,25278,27245,28472,28609,28707,31258,33030,33155,33396,33688,33866,34057,34341,34530,34732,34823,34924,35105,35219,35317,35566,35674,35799,35991,36201,36389,36501,36689,36783,36841,36905,36986,37081,37182,37259,37359,37448,37558,37771,37840,37906,37980,38085,38162,38251,38317,38398,38469,38537,39078,39165,39275,39365,39436,39524,39591,39677,39734,39834,39940,40043,40169,40228,40319,40419,40540,40809,41128,42721,42775,42831,42905,43007,43176,43441,43731,43833,44440,44530,44601,44706,44821,44912,44986,45055,45140,45224,45312,45428,45562,45629,45775,45829,45999,46068,46127,46201,46275,46352,46433,46569,46693,46806,46889,46964,47049,47116", "endColumns": "68,86,79,91,86,77,76,128,91,97,88,116,57,118,63,177,190,137,88,105,90,100,180,113,97,248,107,124,191,209,95,111,187,93,57,63,80,94,100,76,99,88,109,212,68,65,73,104,76,88,65,80,70,67,107,86,109,89,70,87,66,85,56,99,105,102,125,58,90,99,120,70,86,97,53,55,73,101,168,264,289,101,70,89,70,104,114,90,73,68,84,83,87,115,133,66,69,53,169,68,58,73,73,76,80,135,123,112,82,74,84,66,156", "endOffsets": "17714,17801,17881,23399,25195,25273,25350,27369,28559,28702,28791,31370,33083,33269,33455,33861,34052,34190,34425,34631,34818,34919,35100,35214,35312,35561,35669,35794,35986,36196,36292,36496,36684,36778,36836,36900,36981,37076,37177,37254,37354,37443,37553,37766,37835,37901,37975,38080,38157,38246,38312,38393,38464,38532,38640,39160,39270,39360,39431,39519,39586,39672,39729,39829,39935,40038,40164,40223,40314,40414,40535,40606,40891,41221,42770,42826,42900,43002,43171,43436,43726,43828,43899,44525,44596,44701,44816,44907,44981,45050,45135,45219,45307,45423,45557,45624,45694,45824,45994,46063,46122,46196,46270,46347,46428,46564,46688,46801,46884,46959,47044,47111,47268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1067,1131,1217,1290,1350,1437,1501,1563,1625,1693,1758,1812,1930,1988,2049,2105,2180,2306,2392,2469,2560,2644,2724,2865,2943,3023,3145,3231,3309,3365,3416,3482,3550,3624,3695,3770,3842,3920,3990,4063,4167,4251,4328,4416,4505,4579,4652,4737,4786,4864,4930,5010,5093,5155,5219,5282,5351,5459,5562,5663,5762,5822,5877,5957,6037,6115", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "267,345,421,499,596,676,776,925,1003,1062,1126,1212,1285,1345,1432,1496,1558,1620,1688,1753,1807,1925,1983,2044,2100,2175,2301,2387,2464,2555,2639,2719,2860,2938,3018,3140,3226,3304,3360,3411,3477,3545,3619,3690,3765,3837,3915,3985,4058,4162,4246,4323,4411,4500,4574,4647,4732,4781,4859,4925,5005,5088,5150,5214,5277,5346,5454,5557,5658,5757,5817,5872,5952,6032,6110,6187"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,97,98,101,106,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,173,178,179,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2974,3052,3128,3206,3303,4110,4210,4359,9829,9888,10115,10565,10804,10864,10951,11015,11077,11139,11207,11272,11326,11444,11502,11563,11619,11694,11820,11906,11983,12074,12158,12238,12379,12457,12537,12659,12745,12823,12879,12930,12996,13064,13138,13209,13284,13356,13434,13504,13577,13681,13765,13842,13930,14019,14093,14166,14251,14300,14378,14444,14524,14607,14669,14733,14796,14865,14973,15076,15177,15276,15336,15771,16244,16324,16472", "endLines": "5,33,34,35,36,37,45,46,47,97,98,101,106,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,173,178,179,181", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "317,3047,3123,3201,3298,3378,4205,4354,4432,9883,9947,10196,10633,10859,10946,11010,11072,11134,11202,11267,11321,11439,11497,11558,11614,11689,11815,11901,11978,12069,12153,12233,12374,12452,12532,12654,12740,12818,12874,12925,12991,13059,13133,13204,13279,13351,13429,13499,13572,13676,13760,13837,13925,14014,14088,14161,14246,14295,14373,14439,14519,14602,14664,14728,14791,14860,14968,15071,15172,15271,15331,15386,15846,16319,16397,16544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7093,7199,7359,7486,7595,7738,7863,7983,8215,8371,8477,8639,8766,8911,9089,9155,9217", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "7194,7354,7481,7590,7733,7858,7978,8083,8366,8472,8634,8761,8906,9084,9150,9212,9290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "48,49,93,94,96,107,108,168,169,171,172,175,176,180,505,506,507", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4437,4529,9477,9572,9747,10638,10715,15391,15480,15641,15706,15999,16080,16402,47442,47520,47587", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "4524,4604,9567,9666,9824,10710,10799,15475,15557,15701,15766,16075,16159,16467,47515,47582,47702"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,324,419,628,676,743,842,911,1166,1226,1318,1389,1444,1508,1585,1678,1996,2070,2135,2188,2241,2337,2461,2577,2634,2721,2800,2883,2949,3048,3346,3423,3500,3567,3627,3689,3749,3818,3895,3995,4091,4183,4287,4379,4452,4531,4616,4814,5021,5133,5253,5308,6025,6124,6188", "endColumns": "111,156,94,208,47,66,98,68,254,59,91,70,54,63,76,92,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,68,76,99,95,91,103,91,72,78,84,197,206,111,119,54,716,98,63,54", "endOffsets": "162,319,414,623,671,738,837,906,1161,1221,1313,1384,1439,1503,1580,1673,1991,2065,2130,2183,2236,2332,2456,2572,2629,2716,2795,2878,2944,3043,3341,3418,3495,3562,3622,3684,3744,3813,3890,3990,4086,4178,4282,4374,4447,4526,4611,4809,5016,5128,5248,5303,6020,6119,6183,6238"}, "to": {"startLines": "266,267,268,270,274,275,276,277,278,279,280,296,299,301,305,306,311,317,318,326,336,340,341,342,343,344,350,353,358,360,361,362,363,366,368,369,373,377,378,380,382,394,419,420,421,422,423,441,448,449,450,451,453,454,455,472", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22948,23060,23217,23404,24423,24471,24538,24637,24706,24961,25021,26925,27190,27374,27662,27739,28106,28796,28870,29406,30311,30594,30690,30814,30930,30987,31553,31805,32231,32377,32476,32774,32851,33088,33274,33334,33628,34195,34264,34430,34636,36297,38645,38749,38841,38914,38993,40611,41280,41487,41599,41719,41841,42558,42657,44385", "endColumns": "111,156,94,208,47,66,98,68,254,59,91,70,54,63,76,92,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,68,76,99,95,91,103,91,72,78,84,197,206,111,119,54,716,98,63,54", "endOffsets": "23055,23212,23307,23608,24466,24533,24632,24701,24956,25016,25108,26991,27240,27433,27734,27827,28419,28865,28930,29454,30359,30685,30809,30925,30982,31069,31627,31883,32292,32471,32769,32846,32923,33150,33329,33391,33683,34259,34336,34525,34727,36384,38744,38836,38909,38988,39073,40804,41482,41594,41714,41769,42553,42652,42716,44435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,397,467,526,599,671,737,797", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "128,185,247,332,392,462,521,594,666,732,792,859"}, "to": {"startLines": "205,215,217,218,219,223,231,234,237,241,245,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18234,19034,19168,19230,19315,19594,20119,20311,20550,20863,21203,21496", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "18307,19086,19225,19310,19370,19659,20173,20379,20617,20924,21258,21558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b04a7b60dccd9124e74d23c94f4b7089\\transformed\\jetified-facebook-login-18.0.3\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,331,464,577,667,761,838,929,1018,1130,1237,1327,1417,1517,1633,1713,1909,2006,2106,2218,2321", "endColumns": "148,126,132,112,89,93,76,90,88,111,106,89,89,99,115,79,195,96,99,111,102,142", "endOffsets": "199,326,459,572,662,756,833,924,1013,1125,1232,1322,1412,1512,1628,1708,1904,2001,2101,2213,2316,2459"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4684,4833,4960,5093,5206,5296,5390,5467,5558,5647,5759,5866,5956,6046,6146,6262,6342,6538,6635,6735,6847,6950", "endColumns": "148,126,132,112,89,93,76,90,88,111,106,89,89,99,115,79,195,96,99,111,102,142", "endOffsets": "4828,4955,5088,5201,5291,5385,5462,5553,5642,5754,5861,5951,6041,6141,6257,6337,6533,6630,6730,6842,6945,7088"}}]}]}