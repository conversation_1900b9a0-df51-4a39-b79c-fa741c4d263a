{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-108:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,991,1060,1145,1235,1311,1387,1459", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,986,1055,1140,1230,1306,1382,1454,1576"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,505,506,507", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4528,4623,9895,9992,10173,11094,11173,16047,16138,16304,16376,16682,16767,17105,49360,49436,49508", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "4618,4701,9987,10086,10254,11168,11265,16133,16220,16371,16440,16762,16852,17176,49431,49503,49625"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,333,429,634,681,752,857,931,1163,1240,1347,1420,1477,1541,1612,1699,1999,2078,2145,2199,2253,2343,2475,2606,2664,2755,2838,2926,2992,3098,3381,3462,3540,3603,3664,3726,3787,3861,3947,4053,4154,4242,4341,4434,4509,4588,4674,4865,5065,5179,5309,5365,6078,6188,6253", "endColumns": "119,157,95,204,46,70,104,73,231,76,106,72,56,63,70,86,299,78,66,53,53,89,131,130,57,90,82,87,65,105,282,80,77,62,60,61,60,73,85,105,100,87,98,92,74,78,85,190,199,113,129,55,712,109,64,54", "endOffsets": "170,328,424,629,676,747,852,926,1158,1235,1342,1415,1472,1536,1607,1694,1994,2073,2140,2194,2248,2338,2470,2601,2659,2750,2833,2921,2987,3093,3376,3457,3535,3598,3659,3721,3782,3856,3942,4048,4149,4237,4336,4429,4504,4583,4669,4860,5060,5174,5304,5360,6073,6183,6248,6303"}, "to": {"startLines": "267,268,269,271,275,276,277,278,279,280,281,297,300,302,306,307,312,318,319,327,337,340,341,342,343,344,350,353,358,360,361,362,363,366,368,369,373,377,378,380,382,394,419,420,421,422,423,441,448,449,450,451,453,454,455,472", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "23951,24071,24229,24425,25487,25534,25605,25710,25784,26016,26093,28118,28395,28592,28894,28965,29334,30038,30117,30664,31632,31842,31932,32064,32195,32253,32844,33112,33567,33717,33823,34106,34187,34438,34618,34679,34989,35568,35642,35820,36041,37774,40203,40302,40395,40470,40549,42252,42946,43146,43260,43390,43526,44239,44349,46174", "endColumns": "119,157,95,204,46,70,104,73,231,76,106,72,56,63,70,86,299,78,66,53,53,89,131,130,57,90,82,87,65,105,282,80,77,62,60,61,60,73,85,105,100,87,98,92,74,78,85,190,199,113,129,55,712,109,64,54", "endOffsets": "24066,24224,24320,24625,25529,25600,25705,25779,26011,26088,26195,28186,28447,28651,28960,29047,29629,30112,30179,30713,31681,31927,32059,32190,32248,32339,32922,33195,33628,33818,34101,34182,34260,34496,34674,34736,35045,35637,35723,35921,36137,37857,40297,40390,40465,40544,40630,42438,43141,43255,43385,43441,44234,44344,44409,46224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,75", "endOffsets": "262,338"}, "to": {"startLines": "106,512", "startColumns": "4,4", "startOffsets": "10954,49974", "endColumns": "60,79", "endOffsets": "11010,50049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,348,431,485,545,600,680,761,845,948,1051,1137,1215,1296,1381,1482,1577,1649,1741,1829,1917,2025,2107,2199,2278,2377,2455,2555,2650,2741,2828,2935,3012,3093,3206,3312,3382,3450,4169,4864,4942,5065,5173,5228,5328,5420,5491,5588,5689,5746,5833,5884,5962,6071,6146,6220,6287,6353,6401,6487,6587,6660,6710,6757,6826,6884,6947,7154,7328,7472,7537,7628,7703,7798,7888,7983,8060,8151,8235,8341,8430,8525,8580,8719,8769,8824,8904,8976,9049,9118,9194,9285,9355,9407", "endColumns": "73,82,61,73,82,53,59,54,79,80,83,102,102,85,77,80,84,100,94,71,91,87,87,107,81,91,78,98,77,99,94,90,86,106,76,80,112,105,69,67,718,694,77,122,107,54,99,91,70,96,100,56,86,50,77,108,74,73,66,65,47,85,99,72,49,46,68,57,62,206,173,143,64,90,74,94,89,94,76,90,83,105,88,94,54,138,49,54,79,71,72,68,75,90,69,51,80", "endOffsets": "124,207,269,343,426,480,540,595,675,756,840,943,1046,1132,1210,1291,1376,1477,1572,1644,1736,1824,1912,2020,2102,2194,2273,2372,2450,2550,2645,2736,2823,2930,3007,3088,3201,3307,3377,3445,4164,4859,4937,5060,5168,5223,5323,5415,5486,5583,5684,5741,5828,5879,5957,6066,6141,6215,6282,6348,6396,6482,6582,6655,6705,6752,6821,6879,6942,7149,7323,7467,7532,7623,7698,7793,7883,7978,8055,8146,8230,8336,8425,8520,8575,8714,8764,8819,8899,8971,9044,9113,9189,9280,9350,9402,9483"}, "to": {"startLines": "191,192,193,194,195,196,197,201,202,203,204,207,209,210,212,217,221,236,239,240,241,243,244,245,247,251,252,253,254,255,256,257,258,259,260,262,265,266,272,273,274,285,286,287,288,289,290,291,292,293,294,295,296,303,304,305,308,309,310,311,315,320,321,322,323,324,329,330,331,332,333,334,338,339,348,349,351,352,356,357,359,364,371,372,443,444,445,447,452,465,466,467,468,469,470,471,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17922,17996,18079,18141,18215,18298,18352,18672,18727,18807,18888,19121,19307,19410,19564,19921,20211,21249,21504,21599,21671,21831,21919,22007,22177,22486,22578,22657,22756,22834,22934,23029,23120,23207,23314,23471,23732,23845,24630,24700,24768,26454,27149,27227,27350,27458,27513,27613,27705,27776,27873,27974,28031,28656,28707,28785,29052,29127,29201,29268,29784,30184,30270,30370,30443,30493,30780,30849,30907,30970,31177,31351,31686,31751,32674,32749,32927,33017,33399,33476,33633,34265,34805,34894,42541,42596,42735,42891,43446,45671,45743,45816,45885,45961,46052,46122,47576", "endColumns": "73,82,61,73,82,53,59,54,79,80,83,102,102,85,77,80,84,100,94,71,91,87,87,107,81,91,78,98,77,99,94,90,86,106,76,80,112,105,69,67,718,694,77,122,107,54,99,91,70,96,100,56,86,50,77,108,74,73,66,65,47,85,99,72,49,46,68,57,62,206,173,143,64,90,74,94,89,94,76,90,83,105,88,94,54,138,49,54,79,71,72,68,75,90,69,51,80", "endOffsets": "17991,18074,18136,18210,18293,18347,18407,18722,18802,18883,18967,19219,19405,19491,19637,19997,20291,21345,21594,21666,21758,21914,22002,22110,22254,22573,22652,22751,22829,22929,23024,23115,23202,23309,23386,23547,23840,23946,24695,24763,25482,27144,27222,27345,27453,27508,27608,27700,27771,27868,27969,28026,28113,28702,28780,28889,29122,29196,29263,29329,29827,30265,30365,30438,30488,30535,30844,30902,30965,31172,31346,31490,31746,31837,32744,32839,33012,33107,33471,33562,33712,34366,34889,34984,42591,42730,42780,42941,43521,45738,45811,45880,45956,46047,46117,46169,47652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,88", "endOffsets": "137,226"}, "to": {"startLines": "508,509", "startColumns": "4,4", "startOffsets": "49630,49717", "endColumns": "86,88", "endOffsets": "49712,49801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,199,261,343,408,481,540,621,696,764,826", "endColumns": "82,60,61,81,64,72,58,80,74,67,61,71", "endOffsets": "133,194,256,338,403,476,535,616,691,759,821,893"}, "to": {"startLines": "206,216,218,219,220,224,232,235,238,242,246,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19038,19860,20002,20064,20146,20436,20974,21168,21429,21763,22115,22414", "endColumns": "82,60,61,81,64,72,58,80,74,67,61,71", "endOffsets": "19116,19916,20059,20141,20206,20504,21028,21244,21499,21826,22172,22481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3492,3589,3691,3790,3890,3997,4103,17260", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3584,3686,3785,3885,3992,4098,4219,17356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,16857", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,16938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,228,315,415,504,587,669,809,909,1022,1115,1248,1315,1432,1496,1670,1867,2014,2106,2221,2317,2416,2596,2707,2807,3067,3183,3325,3537,3755,3853,3979,4179,4282,4343,4409,4493,4596,4693,4766,4864,4948,5059,5283,5351,5420,5500,5620,5694,5782,5848,5931,6002,6070,6194,6281,6388,6485,6561,6643,6712,6804,6865,6975,7092,7197,7326,7389,7492,7603,7740,7811,7909,8015,8073,8131,8222,8330,8506,8787,9099,9196,9272,9358,9430,9546,9666,9753,9828,9901,9989,10082,10176,10320,10470,10538,10619,10673,10836,10906,10968,11047,11125,11199,11278,11422,11548,11662,11750,11826,11910,11981", "endColumns": "76,95,86,99,88,82,81,139,99,112,92,132,66,116,63,173,196,146,91,114,95,98,179,110,99,259,115,141,211,217,97,125,199,102,60,65,83,102,96,72,97,83,110,223,67,68,79,119,73,87,65,82,70,67,123,86,106,96,75,81,68,91,60,109,116,104,128,62,102,110,136,70,97,105,57,57,90,107,175,280,311,96,75,85,71,115,119,86,74,72,87,92,93,143,149,67,80,53,162,69,61,78,77,73,78,143,125,113,87,75,83,70,171", "endOffsets": "127,223,310,410,499,582,664,804,904,1017,1110,1243,1310,1427,1491,1665,1862,2009,2101,2216,2312,2411,2591,2702,2802,3062,3178,3320,3532,3750,3848,3974,4174,4277,4338,4404,4488,4591,4688,4761,4859,4943,5054,5278,5346,5415,5495,5615,5689,5777,5843,5926,5997,6065,6189,6276,6383,6480,6556,6638,6707,6799,6860,6970,7087,7192,7321,7384,7487,7598,7735,7806,7904,8010,8068,8126,8217,8325,8501,8782,9094,9191,9267,9353,9425,9541,9661,9748,9823,9896,9984,10077,10171,10315,10465,10533,10614,10668,10831,10901,10963,11042,11120,11194,11273,11417,11543,11657,11745,11821,11905,11976,12148"}, "to": {"startLines": "198,199,200,270,282,283,284,301,314,316,317,347,365,367,370,374,375,376,379,381,383,384,385,386,387,388,389,390,391,392,393,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,442,446,456,457,458,459,460,461,462,463,464,473,474,475,476,477,478,479,480,481,482,483,484,485,486,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18412,18489,18585,24325,26200,26289,26372,28452,29684,29832,29945,32541,34371,34501,34741,35050,35224,35421,35728,35926,36142,36238,36337,36517,36628,36728,36988,37104,37246,37458,37676,37862,37988,38188,38291,38352,38418,38502,38605,38702,38775,38873,38957,39068,39292,39360,39429,39509,39629,39703,39791,39857,39940,40011,40079,40635,40722,40829,40926,41002,41084,41153,41245,41306,41416,41533,41638,41767,41830,41933,42044,42181,42443,42785,44414,44472,44530,44621,44729,44905,45186,45498,45595,46229,46315,46387,46503,46623,46710,46785,46858,46946,47039,47133,47277,47427,47495,47657,47711,47874,47944,48006,48085,48163,48237,48316,48460,48586,48700,48788,48864,48948,49019", "endColumns": "76,95,86,99,88,82,81,139,99,112,92,132,66,116,63,173,196,146,91,114,95,98,179,110,99,259,115,141,211,217,97,125,199,102,60,65,83,102,96,72,97,83,110,223,67,68,79,119,73,87,65,82,70,67,123,86,106,96,75,81,68,91,60,109,116,104,128,62,102,110,136,70,97,105,57,57,90,107,175,280,311,96,75,85,71,115,119,86,74,72,87,92,93,143,149,67,80,53,162,69,61,78,77,73,78,143,125,113,87,75,83,70,171", "endOffsets": "18484,18580,18667,24420,26284,26367,26449,28587,29779,29940,30033,32669,34433,34613,34800,35219,35416,35563,35815,36036,36233,36332,36512,36623,36723,36983,37099,37241,37453,37671,37769,37983,38183,38286,38347,38413,38497,38600,38697,38770,38868,38952,39063,39287,39355,39424,39504,39624,39698,39786,39852,39935,40006,40074,40198,40717,40824,40921,40997,41079,41148,41240,41301,41411,41528,41633,41762,41825,41928,42039,42176,42247,42536,42886,44467,44525,44616,44724,44900,45181,45493,45590,45666,46310,46382,46498,46618,46705,46780,46853,46941,47034,47128,47272,47422,47490,47571,47706,47869,47939,48001,48080,48158,48232,48311,48455,48581,48695,48783,48859,48943,49014,49186"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,493,662,749", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "170,258,337,488,657,744,825"}, "to": {"startLines": "92,100,171,175,504,510,511", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9709,10383,16225,16531,49191,49806,49893", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "9774,10466,16299,16677,49355,49888,49969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13a52594bedbd674c7585af371c24fcc\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,152,260,327,395,461,547", "endColumns": "96,107,66,67,65,85,68", "endOffsets": "147,255,322,390,456,542,611"}, "to": {"startLines": "184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "17361,17458,17566,17633,17701,17767,17853", "endColumns": "96,107,66,67,65,85,68", "endOffsets": "17453,17561,17628,17696,17762,17848,17917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1075,1139,1231,1310,1375,1465,1529,1597,1659,1732,1796,1850,1976,2034,2096,2150,2226,2369,2456,2536,2635,2721,2803,2942,3024,3106,3242,3329,3409,3465,3516,3582,3657,3737,3808,3887,3960,4037,4106,4180,4287,4380,4457,4550,4648,4722,4803,4902,4955,5039,5105,5194,5282,5344,5408,5471,5539,5655,5763,5870,5972,6032,6087,6173,6256,6335", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "268,349,429,511,610,706,809,929,1010,1070,1134,1226,1305,1370,1460,1524,1592,1654,1727,1791,1845,1971,2029,2091,2145,2221,2364,2451,2531,2630,2716,2798,2937,3019,3101,3237,3324,3404,3460,3511,3577,3652,3732,3803,3882,3955,4032,4101,4175,4282,4375,4452,4545,4643,4717,4798,4897,4950,5034,5100,5189,5277,5339,5403,5466,5534,5650,5758,5865,5967,6027,6082,6168,6251,6330,6409"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3054,3135,3215,3297,3396,4224,4327,4447,10259,10319,10548,11015,11270,11335,11425,11489,11557,11619,11692,11756,11810,11936,11994,12056,12110,12186,12329,12416,12496,12595,12681,12763,12902,12984,13066,13202,13289,13369,13425,13476,13542,13617,13697,13768,13847,13920,13997,14066,14140,14247,14340,14417,14510,14608,14682,14763,14862,14915,14999,15065,15154,15242,15304,15368,15431,15499,15615,15723,15830,15932,15992,16445,16943,17026,17181", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "318,3130,3210,3292,3391,3487,4322,4442,4523,10314,10378,10635,11089,11330,11420,11484,11552,11614,11687,11751,11805,11931,11989,12051,12105,12181,12324,12411,12491,12590,12676,12758,12897,12979,13061,13197,13284,13364,13420,13471,13537,13612,13692,13763,13842,13915,13992,14061,14135,14242,14335,14412,14505,14603,14677,14758,14857,14910,14994,15060,15149,15237,15299,15363,15426,15494,15610,15718,15825,15927,15987,16042,16526,17021,17100,17255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9779,10640,10739,10851", "endColumns": "115,98,111,102", "endOffsets": "9890,10734,10846,10949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,214", "endColumns": "76,81,76", "endOffsets": "127,209,286"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4706,10091,10471", "endColumns": "76,81,76", "endOffsets": "4778,10168,10543"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7375,7480,7643,7771,7879,8047,8175,8297,8551,8739,8847,9017,9148,9307,9485,9553,9622", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "7475,7638,7766,7874,8042,8170,8292,8401,8734,8842,9012,9143,9302,9480,9548,9617,9704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b04a7b60dccd9124e74d23c94f4b7089\\transformed\\jetified-facebook-login-18.0.3\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,213,354,501,612,697,783,861,952,1049,1165,1282,1381,1480,1591,1719,1801,1887,2073,2167,2268,2383,2499", "endColumns": "157,140,146,110,84,85,77,90,96,115,116,98,98,110,127,81,85,185,93,100,114,115,147", "endOffsets": "208,349,496,607,692,778,856,947,1044,1160,1277,1376,1475,1586,1714,1796,1882,2068,2162,2263,2378,2494,2642"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4783,4941,5082,5229,5340,5425,5511,5589,5680,5777,5893,6010,6109,6208,6319,6447,6529,6615,6801,6895,6996,7111,7227", "endColumns": "157,140,146,110,84,85,77,90,96,115,116,98,98,110,127,81,85,185,93,100,114,115,147", "endOffsets": "4936,5077,5224,5335,5420,5506,5584,5675,5772,5888,6005,6104,6203,6314,6442,6524,6610,6796,6890,6991,7106,7222,7370"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8406", "endColumns": "144", "endOffsets": "8546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,204,272,358,429,490,563,630,692,760,830,890,951,1031,1095,1167,1230,1309,1374,1464,1544,1631,1724,1837,1928,1978,2026,2102,2164,2232,2301,2409,2498,2598", "endColumns": "65,82,67,85,70,60,72,66,61,67,69,59,60,79,63,71,62,78,64,89,79,86,92,112,90,49,47,75,61,67,68,107,88,99,98", "endOffsets": "116,199,267,353,424,485,558,625,687,755,825,885,946,1026,1090,1162,1225,1304,1369,1459,1539,1626,1719,1832,1923,1973,2021,2097,2159,2227,2296,2404,2493,2593,2692"}, "to": {"startLines": "205,208,211,213,214,215,222,223,225,226,227,228,229,230,231,233,234,237,248,249,261,263,264,298,299,313,325,326,328,335,336,345,346,354,355", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18972,19224,19496,19642,19728,19799,20296,20369,20509,20571,20639,20709,20769,20830,20910,21033,21105,21350,22259,22324,23391,23552,23639,28191,28304,29634,30540,30588,30718,31495,31563,32344,32452,33200,33300", "endColumns": "65,82,67,85,70,60,72,66,61,67,69,59,60,79,63,71,62,78,64,89,79,86,92,112,90,49,47,75,61,67,68,107,88,99,98", "endOffsets": "19033,19302,19559,19723,19794,19855,20364,20431,20566,20634,20704,20764,20825,20905,20969,21100,21163,21424,22319,22409,23466,23634,23727,28299,28390,29679,30583,30659,30775,31558,31627,32447,32536,33295,33394"}}]}]}