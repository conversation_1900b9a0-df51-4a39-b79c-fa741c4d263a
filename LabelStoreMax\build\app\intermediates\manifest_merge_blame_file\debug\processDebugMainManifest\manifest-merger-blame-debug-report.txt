1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.velvete.ly"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         <PERSON><PERSON><PERSON> needs it to communicate with the running application
12         to allow setting breakpoints, to provide hot reload, etc.
13    -->
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:22-64
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:22-76
16    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:22-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:22-63
18    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
18-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:5-79
18-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:22-76
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- Profile picture functionality permissions -->
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:5-81
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:22-78
20    <uses-permission android:name="android.permission.CAMERA" />
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:5-65
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:22-62
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:5-80
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:22-77
22    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:5-81
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:22-78
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- Samsung -->
23-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:5-76
23-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:22-73
24    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-86
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-83
25    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-87
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-84
26    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-81
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-78
27    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-83
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-80
28    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-88
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:22-85
29    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-92
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-89
30    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-84
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:22-81
31    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-83
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:22-80
32    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:5-91
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:22-88
33    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-92
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:22-89
34    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:5-93
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:22-90
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
35-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
36    <uses-permission android:name="android.permission.WAKE_LOCK" />
36-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
36-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
37
38    <queries>
38-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
39        <intent>
39-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
40            <action android:name="android.support.customtabs.action.CustomTabsService" />
40-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
40-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
41        </intent>
42        <!-- Added to check the default browser that will host the AuthFlow. -->
43        <intent>
43-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:13:9-17:18
44            <action android:name="android.intent.action.VIEW" />
44-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
44-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
45
46            <data android:scheme="http" />
46-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
46-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
47        </intent>
48
49        <package android:name="com.facebook.katana" /> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
49-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:9-55
49-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:18-52
50        <package android:name="com.android.chrome" />
50-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:9-54
50-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:18-51
51
52        <intent>
52-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:11:9-17:18
53            <action android:name="android.intent.action.VIEW" />
53-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
53-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
54
55            <data
55-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
56                android:mimeType="*/*"
57                android:scheme="*" />
57-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
58        </intent>
59        <intent>
59-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:18:9-27:18
60            <action android:name="android.intent.action.VIEW" />
60-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
60-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
61
62            <category android:name="android.intent.category.BROWSABLE" />
62-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
62-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
63
64            <data
64-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
65                android:host="pay"
66                android:mimeType="*/*"
67                android:scheme="upi" />
67-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
68        </intent>
69        <intent>
69-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:28:9-30:18
70            <action android:name="android.intent.action.MAIN" />
70-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
70-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
71        </intent>
72        <intent>
72-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:31:9-35:18
73            <action android:name="android.intent.action.SEND" />
73-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:13-65
73-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:21-62
74
75            <data android:mimeType="*/*" />
75-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
76        </intent>
77        <intent>
77-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:36:9-38:18
78            <action android:name="rzp.device_token.share" />
78-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:13-61
78-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:21-58
79        </intent> <!-- Needs to be explicitly declared on Android R+ -->
80        <package android:name="com.google.android.apps.maps" />
80-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
80-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
81    </queries>
82
83    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Support for Google Privacy Sandbox adservices API -->
83-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:5-79
83-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:22-76
84    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
84-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:5-88
84-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:22-85
85    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
85-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:5-82
85-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:22-79
86    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE" />
86-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:5-92
86-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:22-89
87    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
87-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:5-83
87-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:22-80
88    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
88-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
88-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
89
90    <uses-feature
90-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
91        android:glEsVersion="0x00020000"
91-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
92        android:required="true" />
92-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
93
94    <permission
94-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
95        android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
95-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
96        android:protectionLevel="signature" />
96-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
97
98    <uses-permission android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
98-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
98-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
99    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
99-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e70112bbc84ddf19fc928e7dec52d465\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
99-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e70112bbc84ddf19fc928e7dec52d465\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
100
101    <application
102        android:name="android.app.Application"
103        android:allowBackup="false"
104        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
104-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
105        android:debuggable="true"
106        android:extractNativeLibs="true"
107        android:fullBackupContent="false"
108        android:icon="@mipmap/ic_launcher"
109        android:label="Label StoreMax"
110        android:supportsRtl="true"
110-->[com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b04a7b60dccd9124e74d23c94f4b7089\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:18-44
111        android:usesCleartextTraffic="true" >
112        <activity
113            android:name="com.velvete.ly.MainActivity"
114            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
115            android:exported="true"
116            android:hardwareAccelerated="true"
117            android:launchMode="singleTop"
118            android:screenOrientation="portrait"
119            android:theme="@style/LaunchTheme"
120            android:windowSoftInputMode="adjustResize" >
121            <meta-data
122                android:name="io.flutter.embedding.android.NormalTheme"
123                android:resource="@style/NormalTheme" />
124
125            <intent-filter>
126                <action android:name="android.intent.action.MAIN" />
126-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
126-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
127
128                <category android:name="android.intent.category.LAUNCHER" />
129            </intent-filter>
130        </activity>
131
132        <meta-data
133            android:name="flutterEmbedding"
134            android:value="2" />
135        <meta-data
136            android:name="com.google.android.geo.API_KEY"
137            android:value="AIzaSyDrKt-lB3zOcD_eLMRdnkUSspv1ovnhn6s" />
138
139        <service
139-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
140            android:name="com.baseflow.geolocator.GeolocatorLocationService"
140-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
141            android:enabled="true"
141-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
142            android:exported="false"
142-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
143            android:foregroundServiceType="location" />
143-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
144
145        <provider
145-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
146            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
146-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
147            android:authorities="com.velvete.ly.flutter.image_provider"
147-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
148            android:exported="false"
148-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
149            android:grantUriPermissions="true" >
149-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
150            <meta-data
150-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
151                android:name="android.support.FILE_PROVIDER_PATHS"
151-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
152                android:resource="@xml/flutter_image_picker_file_paths" />
152-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
153        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
154        <service
154-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
155            android:name="com.google.android.gms.metadata.ModuleDependencies"
155-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
156            android:enabled="false"
156-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
157            android:exported="false" >
157-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
158            <intent-filter>
158-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
159                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
159-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
159-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
160            </intent-filter>
161
162            <meta-data
162-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
163                android:name="photopicker_activity:0:required"
163-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
164                android:value="" />
164-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
165        </service>
166
167        <activity
167-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
168            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
168-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
169            android:exported="false"
169-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
170            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
170-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
171
172        <service
172-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
173            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
173-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
174            android:exported="false"
174-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
175            android:permission="android.permission.BIND_JOB_SERVICE" />
175-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
176        <service
176-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
177            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
177-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
178            android:exported="false" >
178-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
179            <intent-filter>
179-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
180                <action android:name="com.google.firebase.MESSAGING_EVENT" />
180-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
180-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
181            </intent-filter>
182        </service>
183
184        <receiver
184-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
185            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
185-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
186            android:exported="true"
186-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
187            android:permission="com.google.android.c2dm.permission.SEND" >
187-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
188            <intent-filter>
188-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
189                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
189-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
189-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
190            </intent-filter>
191        </receiver>
192
193        <service
193-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
194            android:name="com.google.firebase.components.ComponentDiscoveryService"
194-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
195            android:directBootAware="true"
195-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
196            android:exported="false" >
196-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
197            <meta-data
197-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
198                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
198-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
199                android:value="com.google.firebase.components.ComponentRegistrar" />
199-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
200            <meta-data
200-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
201                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
201-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
202                android:value="com.google.firebase.components.ComponentRegistrar" />
202-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
203            <meta-data
203-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
204                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
204-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
205                android:value="com.google.firebase.components.ComponentRegistrar" />
205-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
206            <meta-data
206-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
207                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
207-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
208                android:value="com.google.firebase.components.ComponentRegistrar" />
208-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
209            <meta-data
209-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
210                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
210-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
211                android:value="com.google.firebase.components.ComponentRegistrar" />
211-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
212            <meta-data
212-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
213                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
213-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
214                android:value="com.google.firebase.components.ComponentRegistrar" />
214-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
215            <meta-data
215-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
216                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
216-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
217                android:value="com.google.firebase.components.ComponentRegistrar" />
217-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
218            <meta-data
218-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
219                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
219-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
220                android:value="com.google.firebase.components.ComponentRegistrar" />
220-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
221            <meta-data
221-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
222                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
222-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
223                android:value="com.google.firebase.components.ComponentRegistrar" />
223-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
224        </service>
225
226        <provider
226-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
227            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
227-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
228            android:authorities="com.velvete.ly.flutterfirebasemessaginginitprovider"
228-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
229            android:exported="false"
229-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
230            android:initOrder="99" />
230-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
231
232        <activity
232-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
233            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
233-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
234            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
234-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
235            android:exported="false"
235-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
236            android:theme="@style/AppTheme" />
236-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
237        <activity
237-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
238            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
238-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
239            android:exported="false"
239-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
240            android:theme="@style/ThemeTransparent" />
240-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
241        <activity
241-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
242            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
242-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
243            android:exported="false"
243-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
244            android:theme="@style/ThemeTransparent" />
244-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
245        <activity
245-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
246            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
246-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
247            android:exported="false"
247-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
248            android:launchMode="singleInstance"
248-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
249            android:theme="@style/ThemeTransparent" />
249-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
250        <activity
250-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
251            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
251-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
252            android:exported="false"
252-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
253            android:launchMode="singleInstance"
253-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
254            android:theme="@style/ThemeTransparent" />
254-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
255
256        <receiver
256-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
257            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
257-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
258            android:enabled="true"
258-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
259            android:exported="false" />
259-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
260
261        <meta-data
261-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
262            android:name="io.flutter.embedded_views_preview"
262-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
263            android:value="true" />
263-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
264
265        <activity
265-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:21:9-65:20
266            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
266-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:22:13-109
267            android:exported="true"
267-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:23:13-36
268            android:launchMode="singleTask" >
268-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:24:13-44
269            <intent-filter>
269-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
270                <action android:name="android.intent.action.VIEW" />
270-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
270-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
271
272                <category android:name="android.intent.category.DEFAULT" />
272-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
272-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
273                <category android:name="android.intent.category.BROWSABLE" />
273-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
273-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
274
275                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
276                <data
276-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
277                    android:host="link-accounts"
278                    android:pathPrefix="/com.velvete.ly/authentication_return"
279                    android:scheme="stripe-auth" />
279-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
280
281                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
282                <data
282-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
283                    android:host="link-native-accounts"
284                    android:pathPrefix="/com.velvete.ly/authentication_return"
285                    android:scheme="stripe-auth" />
285-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
286
287                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
288                <data
288-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
289                    android:host="link-accounts"
290                    android:path="/com.velvete.ly/success"
291                    android:scheme="stripe-auth" />
291-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
292                <data
292-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
293                    android:host="link-accounts"
294                    android:path="/com.velvete.ly/cancel"
295                    android:scheme="stripe-auth" />
295-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
296
297                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
298                <data
298-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
299                    android:host="native-redirect"
300                    android:pathPrefix="/com.velvete.ly"
301                    android:scheme="stripe-auth" />
301-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
302
303                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
304                <data
304-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
305                    android:host="auth-redirect"
306                    android:pathPrefix="/com.velvete.ly"
307                    android:scheme="stripe" />
307-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
308            </intent-filter>
309        </activity>
310        <activity
310-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:66:9-69:77
311            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
311-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:67:13-101
312            android:exported="false"
312-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:68:13-37
313            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
313-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:69:13-74
314        <activity
314-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:70:9-74:58
315            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
315-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:71:13-110
316            android:exported="false"
316-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:72:13-37
317            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
317-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:73:13-74
318            android:windowSoftInputMode="adjustResize" />
318-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:74:13-55
319        <activity
319-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
320            android:name="com.facebook.FacebookActivity"
320-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:21:13-57
321            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
321-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:22:13-96
322            android:theme="@style/com_facebook_activity_theme" />
322-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:23:13-63
323        <activity android:name="com.facebook.CustomTabMainActivity" />
323-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:9-71
323-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:19-68
324        <activity
324-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
325            android:name="com.facebook.CustomTabActivity"
325-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:26:13-58
326            android:exported="true" >
326-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:27:13-36
327            <intent-filter>
327-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
328                <action android:name="android.intent.action.VIEW" />
328-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
328-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
329
330                <category android:name="android.intent.category.DEFAULT" />
330-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
330-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
331                <category android:name="android.intent.category.BROWSABLE" />
331-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
331-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
332
333                <data
333-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
334                    android:host="cct.com.velvete.ly"
335                    android:scheme="fbconnect" />
335-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
336            </intent-filter>
337        </activity>
338        <activity
338-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:8:9-11:69
339            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
339-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:9:13-80
340            android:exported="false"
340-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:10:13-37
341            android:theme="@style/StripePaymentSheetDefaultTheme" />
341-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:11:13-66
342        <activity
342-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:12:9-15:69
343            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
343-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:13:13-82
344            android:exported="false"
344-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:14:13-37
345            android:theme="@style/StripePaymentSheetDefaultTheme" />
345-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:15:13-66
346        <activity
346-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:16:9-19:69
347            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
347-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:17:13-82
348            android:exported="false"
348-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:18:13-37
349            android:theme="@style/StripePaymentSheetDefaultTheme" />
349-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:19:13-66
350        <activity
350-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:20:9-23:69
351            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
351-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:21:13-97
352            android:exported="false"
352-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:22:13-37
353            android:theme="@style/StripePaymentSheetDefaultTheme" />
353-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:23:13-66
354        <activity
354-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:24:9-27:69
355            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
355-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:25:13-118
356            android:exported="false"
356-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:26:13-37
357            android:theme="@style/StripePaymentSheetDefaultTheme" />
357-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:27:13-66
358        <activity
358-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:28:9-31:69
359            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
359-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:29:13-105
360            android:exported="false"
360-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:30:13-37
361            android:theme="@style/StripePaymentSheetDefaultTheme" />
361-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:31:13-66
362        <activity
362-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:32:9-35:69
363            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
363-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:33:13-82
364            android:exported="false"
364-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:34:13-37
365            android:theme="@style/StripePaymentSheetDefaultTheme" />
365-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:35:13-66
366        <activity
366-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:36:9-39:68
367            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
367-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:37:13-94
368            android:exported="false"
368-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:38:13-37
369            android:theme="@style/StripePayLauncherDefaultTheme" />
369-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:39:13-65
370        <activity
370-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:40:9-42:69
371            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
371-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:41:13-121
372            android:theme="@style/StripePaymentSheetDefaultTheme" />
372-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:42:13-66
373        <activity
373-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:43:9-45:69
374            android:name="com.stripe.android.paymentelement.embedded.form.FormActivity"
374-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:44:13-88
375            android:theme="@style/StripePaymentSheetDefaultTheme" />
375-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:45:13-66
376        <activity
376-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:46:9-48:69
377            android:name="com.stripe.android.paymentelement.embedded.manage.ManageActivity"
377-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:47:13-92
378            android:theme="@style/StripePaymentSheetDefaultTheme" />
378-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:48:13-66
379        <activity
379-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:49:9-56:58
380            android:name="com.stripe.android.link.LinkActivity"
380-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:50:13-64
381            android:autoRemoveFromRecents="true"
381-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:51:13-49
382            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
382-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:52:13-115
383            android:exported="false"
383-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:53:13-37
384            android:label="@string/stripe_link"
384-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:54:13-48
385            android:theme="@style/StripeLinkBaseTheme"
385-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:55:13-55
386            android:windowSoftInputMode="adjustResize" />
386-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:56:13-55
387        <activity
387-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:57:9-62:61
388            android:name="com.stripe.android.link.LinkForegroundActivity"
388-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:58:13-74
389            android:autoRemoveFromRecents="true"
389-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:59:13-49
390            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
390-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:60:13-115
391            android:launchMode="singleTop"
391-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:61:13-43
392            android:theme="@style/StripeTransparentTheme" />
392-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:62:13-58
393        <activity
393-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:63:9-80:20
394            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
394-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:64:13-79
395            android:autoRemoveFromRecents="true"
395-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:65:13-49
396            android:exported="true"
396-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:66:13-36
397            android:launchMode="singleInstance"
397-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:67:13-48
398            android:theme="@style/StripeTransparentTheme" >
398-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:68:13-58
399            <intent-filter>
399-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
400                <action android:name="android.intent.action.VIEW" />
400-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
400-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
401
402                <category android:name="android.intent.category.DEFAULT" />
402-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
402-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
403                <category android:name="android.intent.category.BROWSABLE" />
403-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
403-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
404
405                <data
405-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
406                    android:host="complete"
407                    android:path="/com.velvete.ly"
408                    android:scheme="link-popup" />
408-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
409            </intent-filter>
410        </activity>
411        <activity
411-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:8:9-11:69
412            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
412-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:9:13-80
413            android:exported="false"
413-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:10:13-37
414            android:theme="@style/StripePaymentSheetDefaultTheme" />
414-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:11:13-66
415        <activity
415-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:15:9-18:57
416            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
416-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:16:13-78
417            android:exported="false"
417-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:17:13-37
418            android:theme="@style/StripeDefaultTheme" />
418-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:18:13-54
419        <activity
419-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:19:9-22:61
420            android:name="com.stripe.android.view.PaymentRelayActivity"
420-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:20:13-72
421            android:exported="false"
421-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:21:13-37
422            android:theme="@style/StripeTransparentTheme" />
422-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:22:13-58
423        <!--
424        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
425        launched the browser Activity will also handle the return URL deep link.
426        -->
427        <activity
427-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:28:9-32:61
428            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
428-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:29:13-85
429            android:exported="false"
429-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:30:13-37
430            android:launchMode="singleTask"
430-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:31:13-44
431            android:theme="@style/StripeTransparentTheme" />
431-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:32:13-58
432        <activity
432-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:33:9-50:20
433            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
433-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:34:13-88
434            android:exported="true"
434-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:35:13-36
435            android:launchMode="singleTask"
435-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:36:13-44
436            android:theme="@style/StripeTransparentTheme" >
436-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:37:13-58
437            <intent-filter>
437-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
438                <action android:name="android.intent.action.VIEW" />
438-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
438-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
439
440                <category android:name="android.intent.category.DEFAULT" />
440-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
440-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
441                <category android:name="android.intent.category.BROWSABLE" />
441-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
441-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
442
443                <!-- Must match `DefaultReturnUrl#value`. -->
444                <data
444-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
445                    android:host="payment_return_url"
446                    android:path="/com.velvete.ly"
447                    android:scheme="stripesdk" />
447-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
448            </intent-filter>
449        </activity>
450        <activity
450-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:51:9-54:57
451            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
451-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:52:13-114
452            android:exported="false"
452-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:53:13-37
453            android:theme="@style/StripeDefaultTheme" />
453-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:54:13-54
454        <activity
454-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:55:9-58:66
455            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
455-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:56:13-90
456            android:exported="false"
456-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:57:13-37
457            android:theme="@style/StripeGooglePayDefaultTheme" />
457-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:58:13-63
458        <activity
458-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:59:9-62:66
459            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
459-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:60:13-103
460            android:exported="false"
460-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:61:13-37
461            android:theme="@style/StripeGooglePayDefaultTheme" />
461-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:62:13-63
462        <activity
462-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:63:9-66:68
463            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
463-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:64:13-107
464            android:exported="false"
464-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:65:13-37
465            android:theme="@style/StripePayLauncherDefaultTheme" />
465-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:66:13-65
466        <activity
466-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:67:9-70:61
467            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
467-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:68:13-97
468            android:exported="false"
468-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:69:13-37
469            android:theme="@style/StripeTransparentTheme" />
469-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:70:13-58
470        <activity
470-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:8:9-11:54
471            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
471-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:9:13-81
472            android:exported="false"
472-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:10:13-37
473            android:theme="@style/Stripe3DS2Theme" />
473-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:11:13-51
474        <activity
474-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:42:9-50:20
475            android:name="com.razorpay.CheckoutActivity"
475-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:43:13-57
476            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
476-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:44:13-83
477            android:exported="false"
477-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:45:13-37
478            android:theme="@style/CheckoutTheme" >
478-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:46:13-49
479            <intent-filter>
479-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:47:13-49:29
480                <action android:name="android.intent.action.MAIN" />
480-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
480-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
481            </intent-filter>
482        </activity>
483
484        <provider
484-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:52:9-60:20
485            android:name="androidx.startup.InitializationProvider"
485-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:53:13-67
486            android:authorities="com.velvete.ly.androidx-startup"
486-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:54:13-68
487            android:exported="false" >
487-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:55:13-37
488            <meta-data
488-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:57:13-59:52
489                android:name="com.razorpay.RazorpayInitializer"
489-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:58:17-64
490                android:value="androidx.startup" />
490-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:59:17-49
491            <meta-data
491-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
492                android:name="androidx.emoji2.text.EmojiCompatInitializer"
492-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
493                android:value="androidx.startup" />
493-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
494            <meta-data
494-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
495                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
495-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
496                android:value="androidx.startup" />
496-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
497            <meta-data
497-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
498                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
498-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
499                android:value="androidx.startup" />
499-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
500        </provider>
501
502        <activity
502-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:62:9-65:75
503            android:name="com.razorpay.MagicXActivity"
503-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:63:13-55
504            android:exported="false"
504-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:64:13-37
505            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
505-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:65:13-72
506
507        <meta-data
507-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:67:9-69:58
508            android:name="com.razorpay.plugin.googlepay_all"
508-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:68:13-61
509            android:value="com.razorpay.RzpGpayMerged" />
509-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:69:13-55
510
511        <activity
511-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
512            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
512-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
513            android:excludeFromRecents="true"
513-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
514            android:exported="false"
514-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
515            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
515-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
516        <!--
517            Service handling Google Sign-In user revocation. For apps that do not integrate with
518            Google Sign-In, this service will never be started.
519        -->
520        <service
520-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
521            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
521-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
522            android:exported="true"
522-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
523            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
523-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
524            android:visibleToInstantApps="true" />
524-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
525        <!--
526         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
527         with the application context. This config is merged in with the host app's manifest,
528         but there can only be one provider with the same authority activated at any given
529         point; so if the end user has two or more different apps that use Facebook SDK, only the
530         first one will be able to use the provider. To work around this problem, we use the
531         following placeholder in the authority to identify each host application as if it was
532         a completely different provider.
533        -->
534        <provider
534-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
535            android:name="com.facebook.internal.FacebookInitProvider"
535-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:33:13-70
536            android:authorities="com.velvete.ly.FacebookInitProvider"
536-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:34:13-72
537            android:exported="false" />
537-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:35:13-37
538
539        <receiver
539-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
540            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
540-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:38:13-86
541            android:exported="false" >
541-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:39:13-37
542            <intent-filter>
542-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
543                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
543-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:17-95
543-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:25-92
544            </intent-filter>
545        </receiver>
546        <receiver
546-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
547            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
547-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:45:13-118
548            android:exported="false" >
548-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:46:13-37
549            <intent-filter>
549-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
550                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
550-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:17-103
550-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:25-100
551            </intent-filter>
552        </receiver>
553        <receiver
553-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
554            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
554-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
555            android:exported="true"
555-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
556            android:permission="com.google.android.c2dm.permission.SEND" >
556-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
557            <intent-filter>
557-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
558                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
558-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
558-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
559            </intent-filter>
560
561            <meta-data
561-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
562                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
562-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
563                android:value="true" />
563-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
564        </receiver>
565        <!--
566             FirebaseMessagingService performs security checks at runtime,
567             but set to not exported to explicitly avoid allowing another app to call it.
568        -->
569        <service
569-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
570            android:name="com.google.firebase.messaging.FirebaseMessagingService"
570-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
571            android:directBootAware="true"
571-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
572            android:exported="false" >
572-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
573            <intent-filter android:priority="-500" >
573-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
574                <action android:name="com.google.firebase.MESSAGING_EVENT" />
574-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
574-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
575            </intent-filter>
576        </service> <!-- Needs to be explicitly declared on P+ -->
577        <uses-library
577-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
578            android:name="org.apache.http.legacy"
578-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
579            android:required="false" />
579-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
580
581        <activity
581-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
582            android:name="com.google.android.gms.common.api.GoogleApiActivity"
582-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
583            android:exported="false"
583-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
584            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
584-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
585
586        <provider
586-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
587            android:name="com.google.firebase.provider.FirebaseInitProvider"
587-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
588            android:authorities="com.velvete.ly.firebaseinitprovider"
588-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
589            android:directBootAware="true"
589-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
590            android:exported="false"
590-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
591            android:initOrder="100" />
591-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
592
593        <uses-library
593-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
594            android:name="androidx.window.extensions"
594-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
595            android:required="false" />
595-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
596        <uses-library
596-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
597            android:name="androidx.window.sidecar"
597-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
598            android:required="false" />
598-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
599
600        <meta-data
600-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
601            android:name="com.google.android.gms.version"
601-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
602            android:value="@integer/google_play_services_version" />
602-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
603
604        <receiver
604-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
605            android:name="androidx.profileinstaller.ProfileInstallReceiver"
605-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
606            android:directBootAware="false"
606-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
607            android:enabled="true"
607-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
608            android:exported="true"
608-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
609            android:permission="android.permission.DUMP" >
609-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
610            <intent-filter>
610-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
611                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
611-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
611-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
612            </intent-filter>
613            <intent-filter>
613-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
614                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
614-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
614-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
615            </intent-filter>
616            <intent-filter>
616-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
617                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
617-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
617-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
618            </intent-filter>
619            <intent-filter>
619-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
620                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
620-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
620-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
621            </intent-filter>
622        </receiver>
623
624        <service
624-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
625            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
625-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
626            android:exported="false" >
626-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
627            <meta-data
627-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
628                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
628-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
629                android:value="cct" />
629-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
630        </service>
631        <service
631-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
632            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
632-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
633            android:exported="false"
633-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
634            android:permission="android.permission.BIND_JOB_SERVICE" >
634-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
635        </service>
636
637        <receiver
637-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
638            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
638-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
639            android:exported="false" />
639-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
640
641        <meta-data
641-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
642            android:name="aia-compat-api-min-version"
642-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
643            android:value="1" /> <!-- The activities will be merged into the manifest of the hosting app. -->
643-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
644        <activity
644-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
645            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
645-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
646            android:exported="false"
646-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
647            android:stateNotNeeded="true"
647-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
648            android:theme="@style/Theme.PlayCore.Transparent" />
648-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
649    </application>
650
651</manifest>
