{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-108:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,189,374,477,688,735,801,890,960,1219,1283,1371,1437,1491,1555,1631,1723,2039,2118,2185,2238,2291,2369,2492,2605,2662,2738,2815,2899,2975,3083,3392,3470,3547,3618,3678,3745,3805,3880,3962,4059,4158,4249,4343,4441,4516,4595,4683,4882,5089,5202,5343,5406,6105,6212,6276", "endColumns": "133,184,102,210,46,65,88,69,258,63,87,65,53,63,75,91,315,78,66,52,52,77,122,112,56,75,76,83,75,107,308,77,76,70,59,66,59,74,81,96,98,90,93,97,74,78,87,198,206,112,140,62,698,106,63,61", "endOffsets": "184,369,472,683,730,796,885,955,1214,1278,1366,1432,1486,1550,1626,1718,2034,2113,2180,2233,2286,2364,2487,2600,2657,2733,2810,2894,2970,3078,3387,3465,3542,3613,3673,3740,3800,3875,3957,4054,4153,4244,4338,4436,4511,4590,4678,4877,5084,5197,5338,5401,6100,6207,6271,6333"}, "to": {"startLines": "260,261,262,264,268,269,270,271,272,273,274,290,293,295,299,300,305,311,312,320,330,334,335,336,337,338,344,347,352,354,355,356,357,360,362,363,367,371,372,374,376,388,413,414,415,416,417,435,442,443,444,445,447,448,449,466", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "23288,23422,23607,23812,24860,24907,24973,25062,25132,25391,25455,27359,27613,27811,28121,28197,28567,29265,29344,29879,30814,31109,31187,31310,31423,31480,32017,32260,32697,32845,32953,33262,33340,33590,33785,33845,34155,34738,34813,34987,35182,36846,39248,39342,39440,39515,39594,41258,41957,42164,42277,42418,42554,43253,43360,45153", "endColumns": "133,184,102,210,46,65,88,69,258,63,87,65,53,63,75,91,315,78,66,52,52,77,122,112,56,75,76,83,75,107,308,77,76,70,59,66,59,74,81,96,98,90,93,97,74,78,87,198,206,112,140,62,698,106,63,61", "endOffsets": "23417,23602,23705,24018,24902,24968,25057,25127,25386,25450,25538,27420,27662,27870,28192,28284,28878,29339,29406,29927,30862,31182,31305,31418,31475,31551,32089,32339,32768,32948,33257,33335,33412,33656,33840,33907,34210,34808,34890,35079,35276,36932,39337,39435,39510,39589,39677,41452,42159,42272,42413,42476,43248,43355,43419,45210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,215", "endColumns": "79,79,75", "endOffsets": "130,210,286"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4678,10152,10534", "endColumns": "79,79,75", "endOffsets": "4753,10227,10605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,189,258,345,420,481,547,613,678,751,818,887,950,1024,1088,1157,1221,1300,1365,1448,1526,1608,1697,1805,1885,1937,1985,2061,2125,2196,2269,2361,2440,2538", "endColumns": "60,72,68,86,74,60,65,65,64,72,66,68,62,73,63,68,63,78,64,82,77,81,88,107,79,51,47,75,63,70,72,91,78,97,92", "endOffsets": "111,184,253,340,415,476,542,608,673,746,813,882,945,1019,1083,1152,1216,1295,1360,1443,1521,1603,1692,1800,1880,1932,1980,2056,2120,2191,2264,2356,2435,2533,2626"}, "to": {"startLines": "198,201,204,206,207,208,215,216,218,219,220,221,222,223,224,226,227,230,241,242,254,256,257,291,292,306,318,319,321,328,329,339,340,348,349", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18371,18604,18861,19015,19102,19177,19681,19747,19883,19948,20021,20088,20157,20220,20294,20416,20485,20721,21640,21705,22735,22891,22973,27425,27533,28883,29755,29803,29932,30670,30741,31556,31648,32344,32442", "endColumns": "60,72,68,86,74,60,65,65,64,72,66,68,62,73,63,68,63,78,64,82,77,81,88,107,79,51,47,75,63,70,72,91,78,97,92", "endOffsets": "18427,18672,18925,19097,19172,19233,19742,19808,19943,20016,20083,20152,20215,20289,20353,20480,20544,20795,21700,21783,22808,22968,23057,27528,27608,28930,29798,29874,29991,30736,30809,31643,31722,32437,32530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d75c5a854b2b1764f3caa76d8e9ef40e\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "333", "startColumns": "4", "startOffsets": "31019", "endColumns": "89", "endOffsets": "31104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,96", "endOffsets": "139,236"}, "to": {"startLines": "502,503", "startColumns": "4,4", "startOffsets": "48530,48619", "endColumns": "88,96", "endOffsets": "48614,48711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,266,338,419,481,549,603,678,763,843,940,1039,1124,1209,1293,1380,1478,1576,1647,1742,1834,1920,2030,2115,2210,2288,2390,2464,2555,2647,2734,2818,2923,2991,3069,3185,3295,3374,3437,4132,4807,4880,4975,5080,5135,5223,5312,5376,5465,5561,5617,5705,5753,5837,5951,6026,6096,6163,6229,6278,6356,6450,6525,6570,6622,6688,6746,6808,6995,7166,7296,7361,7448,7518,7610,7690,7776,7847,7938,8010,8119,8206,8297,8352,8483,8537,8595,8668,8738,8809,8874,8943,9033,9104,9156", "endColumns": "67,76,65,71,80,61,67,53,74,84,79,96,98,84,84,83,86,97,97,70,94,91,85,109,84,94,77,101,73,90,91,86,83,104,67,77,115,109,78,62,694,674,72,94,104,54,87,88,63,88,95,55,87,47,83,113,74,69,66,65,48,77,93,74,44,51,65,57,61,186,170,129,64,86,69,91,79,85,70,90,71,108,86,90,54,130,53,57,72,69,70,64,68,89,70,51,73", "endOffsets": "118,195,261,333,414,476,544,598,673,758,838,935,1034,1119,1204,1288,1375,1473,1571,1642,1737,1829,1915,2025,2110,2205,2283,2385,2459,2550,2642,2729,2813,2918,2986,3064,3180,3290,3369,3432,4127,4802,4875,4970,5075,5130,5218,5307,5371,5460,5556,5612,5700,5748,5832,5946,6021,6091,6158,6224,6273,6351,6445,6520,6565,6617,6683,6741,6803,6990,7161,7291,7356,7443,7513,7605,7685,7771,7842,7933,8005,8114,8201,8292,8347,8478,8532,8590,8663,8733,8804,8869,8938,9028,9099,9151,9225"}, "to": {"startLines": "184,185,186,187,188,189,190,194,195,196,197,200,202,203,205,210,214,229,232,233,234,236,237,238,240,244,245,246,247,248,249,250,251,252,253,255,258,259,265,266,267,278,279,280,281,282,283,284,285,286,287,288,289,296,297,298,301,302,303,304,308,313,314,315,316,317,322,323,324,325,326,327,331,332,342,343,345,346,350,351,353,358,365,366,437,438,439,441,446,459,460,461,462,463,464,465,481", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17346,17414,17491,17557,17629,17710,17772,18077,18131,18206,18291,18507,18677,18776,18930,19298,19594,20623,20874,20972,21043,21206,21298,21384,21555,21859,21954,22032,22134,22208,22299,22391,22478,22562,22667,22813,23062,23178,24023,24102,24165,25786,26461,26534,26629,26734,26789,26877,26966,27030,27119,27215,27271,27875,27923,28007,28289,28364,28434,28501,29037,29411,29489,29583,29658,29703,29996,30062,30120,30182,30369,30540,30867,30932,31855,31925,32094,32174,32535,32606,32773,33417,33977,34064,41552,41607,41738,41899,42481,44665,44735,44806,44871,44940,45030,45101,46530", "endColumns": "67,76,65,71,80,61,67,53,74,84,79,96,98,84,84,83,86,97,97,70,94,91,85,109,84,94,77,101,73,90,91,86,83,104,67,77,115,109,78,62,694,674,72,94,104,54,87,88,63,88,95,55,87,47,83,113,74,69,66,65,48,77,93,74,44,51,65,57,61,186,170,129,64,86,69,91,79,85,70,90,71,108,86,90,54,130,53,57,72,69,70,64,68,89,70,51,73", "endOffsets": "17409,17486,17552,17624,17705,17767,17835,18126,18201,18286,18366,18599,18771,18856,19010,19377,19676,20716,20967,21038,21133,21293,21379,21489,21635,21949,22027,22129,22203,22294,22386,22473,22557,22662,22730,22886,23173,23283,24097,24160,24855,26456,26529,26624,26729,26784,26872,26961,27025,27114,27210,27266,27354,27918,28002,28116,28359,28429,28496,28562,29081,29484,29578,29653,29698,29750,30057,30115,30177,30364,30535,30665,30927,31014,31920,32012,32169,32255,32601,32692,32840,33521,34059,34150,41602,41733,41787,41952,42549,44730,44801,44866,44935,45025,45096,45148,46599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,214,292,394,480,559,637,781,883,978,1062,1190,1254,1378,1443,1623,1833,1966,2058,2156,2247,2343,2518,2618,2720,2966,3065,3189,3404,3625,3721,3835,4027,4121,4183,4251,4333,4424,4516,4592,4687,4776,4876,5105,5179,5246,5317,5428,5518,5606,5683,5769,5843,5925,6032,6121,6231,6329,6404,6491,6565,6654,6714,6812,6912,7015,7151,7213,7307,7408,7534,7608,7703,7810,7874,7939,8024,8140,8328,8598,8886,8982,9051,9141,9215,9335,9454,9545,9628,9699,9782,9865,9955,10068,10224,10295,10366,10435,10607,10675,10741,10827,10899,10977,11053,11174,11283,11380,11468,11548,11636,11708", "endColumns": "73,84,77,101,85,78,77,143,101,94,83,127,63,123,64,179,209,132,91,97,90,95,174,99,101,245,98,123,214,220,95,113,191,93,61,67,81,90,91,75,94,88,99,228,73,66,70,110,89,87,76,85,73,81,106,88,109,97,74,86,73,88,59,97,99,102,135,61,93,100,125,73,94,106,63,64,84,115,187,269,287,95,68,89,73,119,118,90,82,70,82,82,89,112,155,70,70,68,171,67,65,85,71,77,75,120,108,96,87,79,87,71,148", "endOffsets": "124,209,287,389,475,554,632,776,878,973,1057,1185,1249,1373,1438,1618,1828,1961,2053,2151,2242,2338,2513,2613,2715,2961,3060,3184,3399,3620,3716,3830,4022,4116,4178,4246,4328,4419,4511,4587,4682,4771,4871,5100,5174,5241,5312,5423,5513,5601,5678,5764,5838,5920,6027,6116,6226,6324,6399,6486,6560,6649,6709,6807,6907,7010,7146,7208,7302,7403,7529,7603,7698,7805,7869,7934,8019,8135,8323,8593,8881,8977,9046,9136,9210,9330,9449,9540,9623,9694,9777,9860,9950,10063,10219,10290,10361,10430,10602,10670,10736,10822,10894,10972,11048,11169,11278,11375,11463,11543,11631,11703,11852"}, "to": {"startLines": "191,192,193,263,275,276,277,294,307,309,310,341,359,361,364,368,369,370,373,375,377,378,379,380,381,382,383,384,385,386,387,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,436,440,450,451,452,453,454,455,456,457,458,467,468,469,470,471,472,473,474,475,476,477,478,479,480,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17840,17914,17999,23710,25543,25629,25708,27667,28935,29086,29181,31727,33526,33661,33912,34215,34395,34605,34895,35084,35281,35372,35468,35643,35743,35845,36091,36190,36314,36529,36750,36937,37051,37243,37337,37399,37467,37549,37640,37732,37808,37903,37992,38092,38321,38395,38462,38533,38644,38734,38822,38899,38985,39059,39141,39682,39771,39881,39979,40054,40141,40215,40304,40364,40462,40562,40665,40801,40863,40957,41058,41184,41457,41792,43424,43488,43553,43638,43754,43942,44212,44500,44596,45215,45305,45379,45499,45618,45709,45792,45863,45946,46029,46119,46232,46388,46459,46604,46673,46845,46913,46979,47065,47137,47215,47291,47412,47521,47618,47706,47786,47874,47946", "endColumns": "73,84,77,101,85,78,77,143,101,94,83,127,63,123,64,179,209,132,91,97,90,95,174,99,101,245,98,123,214,220,95,113,191,93,61,67,81,90,91,75,94,88,99,228,73,66,70,110,89,87,76,85,73,81,106,88,109,97,74,86,73,88,59,97,99,102,135,61,93,100,125,73,94,106,63,64,84,115,187,269,287,95,68,89,73,119,118,90,82,70,82,82,89,112,155,70,70,68,171,67,65,85,71,77,75,120,108,96,87,79,87,71,148", "endOffsets": "17909,17994,18072,23807,25624,25703,25781,27806,29032,29176,29260,31850,33585,33780,33972,34390,34600,34733,34982,35177,35367,35463,35638,35738,35840,36086,36185,36309,36524,36745,36841,37046,37238,37332,37394,37462,37544,37635,37727,37803,37898,37987,38087,38316,38390,38457,38528,38639,38729,38817,38894,38980,39054,39136,39243,39766,39876,39974,40049,40136,40210,40299,40359,40457,40557,40660,40796,40858,40952,41053,41179,41253,41547,41894,43483,43548,43633,43749,43937,44207,44495,44591,44660,45300,45374,45494,45613,45704,45787,45858,45941,46024,46114,46227,46383,46454,46525,46668,46840,46908,46974,47060,47132,47210,47286,47407,47516,47613,47701,47781,47869,47941,48090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,16852", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,16931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,989,1053,1136,1224,1298,1377,1443", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,984,1048,1131,1219,1293,1372,1438,1559"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,499,500,501", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4495,4590,9956,10053,10232,11165,11247,16073,16162,16331,16395,16681,16764,17098,48264,48343,48409", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "4585,4673,10048,10147,10314,11242,11338,16157,16244,16390,16454,16759,16847,17167,48338,48404,48525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b04a7b60dccd9124e74d23c94f4b7089\\transformed\\jetified-facebook-login-18.0.3\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,353,506,617,701,789,865,954,1044,1159,1272,1369,1466,1572,1704,1786,1872,2066,2162,2268,2383,2499", "endColumns": "168,128,152,110,83,87,75,88,89,114,112,96,96,105,131,81,85,193,95,105,114,115,158", "endOffsets": "219,348,501,612,696,784,860,949,1039,1154,1267,1364,1461,1567,1699,1781,1867,2061,2157,2263,2378,2494,2653"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4758,4927,5056,5209,5320,5404,5492,5568,5657,5747,5862,5975,6072,6169,6275,6407,6489,6575,6769,6865,6971,7086,7202", "endColumns": "168,128,152,110,83,87,75,88,89,114,112,96,96,105,131,81,85,193,95,105,114,115,158", "endOffsets": "4922,5051,5204,5315,5399,5487,5563,5652,5742,5857,5970,6067,6164,6270,6402,6484,6570,6764,6860,6966,7081,7197,7356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7361,7472,7656,7794,7903,8071,8209,8331,8618,8788,8896,9081,9218,9390,9562,9633,9701", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "7467,7651,7789,7898,8066,8204,8326,8436,8783,8891,9076,9213,9385,9557,9628,9696,9784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8441", "endColumns": "176", "endOffsets": "8613"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1032,1096,1195,1270,1329,1439,1501,1570,1628,1700,1761,1816,1919,1976,2036,2091,2172,2292,2375,2453,2549,2635,2723,2858,2941,3021,3161,3255,3337,3390,3441,3507,3583,3665,3736,3820,3897,3972,4051,4128,4233,4329,4406,4498,4595,4669,4754,4851,4903,4986,5053,5141,5228,5290,5354,5417,5483,5581,5687,5781,5888,5945,6000,6085,6170,6247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "258,339,415,492,582,662,761,881,964,1027,1091,1190,1265,1324,1434,1496,1565,1623,1695,1756,1811,1914,1971,2031,2086,2167,2287,2370,2448,2544,2630,2718,2853,2936,3016,3156,3250,3332,3385,3436,3502,3578,3660,3731,3815,3892,3967,4046,4123,4228,4324,4401,4493,4590,4664,4749,4846,4898,4981,5048,5136,5223,5285,5349,5412,5478,5576,5682,5776,5883,5940,5995,6080,6165,6242,6315"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3067,3148,3224,3301,3391,4193,4292,4412,10319,10382,10610,11090,11343,11402,11512,11574,11643,11701,11773,11834,11889,11992,12049,12109,12164,12245,12365,12448,12526,12622,12708,12796,12931,13014,13094,13234,13328,13410,13463,13514,13580,13656,13738,13809,13893,13970,14045,14124,14201,14306,14402,14479,14571,14668,14742,14827,14924,14976,15059,15126,15214,15301,15363,15427,15490,15556,15654,15760,15854,15961,16018,16459,16936,17021,17172", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "308,3143,3219,3296,3386,3466,4287,4407,4490,10377,10441,10704,11160,11397,11507,11569,11638,11696,11768,11829,11884,11987,12044,12104,12159,12240,12360,12443,12521,12617,12703,12791,12926,13009,13089,13229,13323,13405,13458,13509,13575,13651,13733,13804,13888,13965,14040,14119,14196,14301,14397,14474,14566,14663,14737,14822,14919,14971,15054,15121,15209,15296,15358,15422,15485,15551,15649,15755,15849,15956,16013,16068,16539,17016,17093,17240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9860,10709,10810,10925", "endColumns": "95,100,114,103", "endOffsets": "9951,10805,10920,11024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,190,254,339,402,472,530,604,678,746,807", "endColumns": "74,59,63,84,62,69,57,73,73,67,60,70", "endOffsets": "125,185,249,334,397,467,525,599,673,741,802,873"}, "to": {"startLines": "199,209,211,212,213,217,225,228,231,235,239,243", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18432,19238,19382,19446,19531,19813,20358,20549,20800,21138,21494,21788", "endColumns": "74,59,63,84,62,69,57,73,73,67,60,70", "endOffsets": "18502,19293,19441,19526,19589,19878,20411,20618,20869,21201,21550,21854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3471,3568,3670,3772,3873,3976,4083,17245", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3563,3665,3767,3868,3971,4078,4188,17341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,76", "endOffsets": "258,335"}, "to": {"startLines": "106,506", "startColumns": "4,4", "startOffsets": "11029,48871", "endColumns": "60,80", "endOffsets": "11085,48947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,346,483,652,731", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "171,259,341,478,647,726,802"}, "to": {"startLines": "92,100,171,175,498,504,505", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9789,10446,16249,16544,48095,48716,48795", "endColumns": "70,87,81,136,168,78,75", "endOffsets": "9855,10529,16326,16676,48259,48790,48866"}}]}]}