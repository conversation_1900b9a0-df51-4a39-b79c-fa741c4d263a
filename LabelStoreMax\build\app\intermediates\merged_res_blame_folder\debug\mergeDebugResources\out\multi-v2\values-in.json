{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-108:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1047,1112,1206,1271,1330,1417,1479,1541,1601,1667,1729,1783,1895,1952,2013,2067,2139,2265,2351,2429,2522,2608,2692,2831,2912,2993,3128,3218,3300,3353,3405,3471,3543,3627,3698,3778,3853,3929,4002,4077,4175,4260,4335,4427,4521,4595,4668,4762,4814,4896,4965,5050,5137,5199,5263,5326,5398,5501,5606,5701,5804,5861,5917,5997,6078,6156", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "264,343,419,498,588,673,779,895,978,1042,1107,1201,1266,1325,1412,1474,1536,1596,1662,1724,1778,1890,1947,2008,2062,2134,2260,2346,2424,2517,2603,2687,2826,2907,2988,3123,3213,3295,3348,3400,3466,3538,3622,3693,3773,3848,3924,3997,4072,4170,4255,4330,4422,4516,4590,4663,4757,4809,4891,4960,5045,5132,5194,5258,5321,5393,5496,5601,5696,5799,5856,5912,5992,6073,6151,6229"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3026,3105,3181,3260,3350,4165,4271,4387,9981,10045,10274,10737,10976,11035,11122,11184,11246,11306,11372,11434,11488,11600,11657,11718,11772,11844,11970,12056,12134,12227,12313,12397,12536,12617,12698,12833,12923,13005,13058,13110,13176,13248,13332,13403,13483,13558,13634,13707,13782,13880,13965,14040,14132,14226,14300,14373,14467,14519,14601,14670,14755,14842,14904,14968,15031,15103,15206,15311,15406,15509,15566,16011,16477,16558,16708", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "314,3100,3176,3255,3345,3430,4266,4382,4465,10040,10105,10363,10797,11030,11117,11179,11241,11301,11367,11429,11483,11595,11652,11713,11767,11839,11965,12051,12129,12222,12308,12392,12531,12612,12693,12828,12918,13000,13053,13105,13171,13243,13327,13398,13478,13553,13629,13702,13777,13875,13960,14035,14127,14221,14295,14368,14462,14514,14596,14665,14750,14837,14899,14963,15026,15098,15201,15306,15401,15504,15561,15617,16086,16553,16631,16781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,499,500,501", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4470,4560,9625,9723,9895,10802,10885,15622,15709,15874,15944,16227,16309,16636,48260,48338,48404", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "4555,4637,9718,9818,9976,10880,10971,15704,15789,15939,16006,16304,16387,16703,48333,48399,48518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,2812", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,646,733,837,953,1036,1114,1205,1298,1393,1487,1587,1680,1775,1869,1960,2051,2137,2240,2345,2446,2550,2659,2767,2927,16392", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,641,728,832,948,1031,1109,1200,1293,1388,1482,1582,1675,1770,1864,1955,2046,2132,2235,2340,2441,2545,2654,2762,2922,3021,16472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7255,7362,7526,7652,7758,7913,8040,8155,8393,8559,8664,8828,8954,9109,9253,9317,9377", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "7357,7521,7647,7753,7908,8035,8150,8256,8554,8659,8823,8949,9104,9248,9312,9372,9451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b04a7b60dccd9124e74d23c94f4b7089\\transformed\\jetified-facebook-login-18.0.3\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,354,490,614,695,780,854,942,1030,1147,1256,1346,1436,1537,1657,1738,1822,2011,2107,2209,2327,2436", "endColumns": "164,133,135,123,80,84,73,87,87,116,108,89,89,100,119,80,83,188,95,101,117,108,153", "endOffsets": "215,349,485,609,690,775,849,937,1025,1142,1251,1341,1431,1532,1652,1733,1817,2006,2102,2204,2322,2431,2585"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4720,4885,5019,5155,5279,5360,5445,5519,5607,5695,5812,5921,6011,6101,6202,6322,6403,6487,6676,6772,6874,6992,7101", "endColumns": "164,133,135,123,80,84,73,87,87,116,108,89,89,100,119,80,83,188,95,101,117,108,153", "endOffsets": "4880,5014,5150,5274,5355,5440,5514,5602,5690,5807,5916,6006,6096,6197,6317,6398,6482,6671,6767,6869,6987,7096,7250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8261", "endColumns": "131", "endOffsets": "8388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9525,10368,10466,10575", "endColumns": "99,97,108,100", "endOffsets": "9620,10461,10570,10671"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3435,3530,3632,3729,3826,3932,4050,16786", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3525,3627,3724,3821,3927,4045,4160,16882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,222,304,404,495,577,658,801,901,1007,1100,1220,1280,1392,1459,1669,1901,2034,2126,2235,2328,2427,2605,2713,2806,3084,3192,3320,3528,3740,3837,3949,4166,4260,4320,4390,4478,4579,4671,4744,4840,4924,5030,5266,5332,5400,5478,5594,5678,5767,5835,5916,5987,6063,6193,6280,6384,6478,6550,6629,6696,6784,6844,6948,7068,7175,7304,7367,7461,7565,7688,7759,7851,7962,8022,8090,8168,8272,8450,8728,9032,9132,9206,9296,9369,9487,9604,9689,9761,9832,9918,10003,10089,10228,10371,10439,10511,10575,10763,10834,10892,10968,11043,11120,11201,11319,11438,11543,11630,11708,11797,11866", "endColumns": "75,90,81,99,90,81,80,142,99,105,92,119,59,111,66,209,231,132,91,108,92,98,177,107,92,277,107,127,207,211,96,111,216,93,59,69,87,100,91,72,95,83,105,235,65,67,77,115,83,88,67,80,70,75,129,86,103,93,71,78,66,87,59,103,119,106,128,62,93,103,122,70,91,110,59,67,77,103,177,277,303,99,73,89,72,117,116,84,71,70,85,84,85,138,142,67,71,63,187,70,57,75,74,76,80,117,118,104,86,77,88,68,161", "endOffsets": "126,217,299,399,490,572,653,796,896,1002,1095,1215,1275,1387,1454,1664,1896,2029,2121,2230,2323,2422,2600,2708,2801,3079,3187,3315,3523,3735,3832,3944,4161,4255,4315,4385,4473,4574,4666,4739,4835,4919,5025,5261,5327,5395,5473,5589,5673,5762,5830,5911,5982,6058,6188,6275,6379,6473,6545,6624,6691,6779,6839,6943,7063,7170,7299,7362,7456,7560,7683,7754,7846,7957,8017,8085,8163,8267,8445,8723,9027,9127,9201,9291,9364,9482,9599,9684,9756,9827,9913,9998,10084,10223,10366,10434,10506,10570,10758,10829,10887,10963,11038,11115,11196,11314,11433,11538,11625,11703,11792,11861,12023"}, "to": {"startLines": "191,192,193,263,275,276,277,294,307,309,310,341,359,361,364,368,369,370,373,375,377,378,379,380,381,382,383,384,385,386,387,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,436,440,450,451,452,453,454,455,456,457,458,467,468,469,470,471,472,473,474,475,476,477,478,479,480,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17376,17452,17543,23238,25160,25251,25333,27378,28615,28762,28868,31461,33296,33420,33654,33957,34167,34399,34687,34879,35083,35176,35275,35453,35561,35654,35932,36040,36168,36376,36588,36784,36896,37113,37207,37267,37337,37425,37526,37618,37691,37787,37871,37977,38213,38279,38347,38425,38541,38625,38714,38782,38863,38934,39010,39572,39659,39763,39857,39929,40008,40075,40163,40223,40327,40447,40554,40683,40746,40840,40944,41067,41367,41689,43383,43443,43511,43589,43693,43871,44149,44453,44553,45184,45274,45347,45465,45582,45667,45739,45810,45896,45981,46067,46206,46349,46417,46574,46638,46826,46897,46955,47031,47106,47183,47264,47382,47501,47606,47693,47771,47860,47929", "endColumns": "75,90,81,99,90,81,80,142,99,105,92,119,59,111,66,209,231,132,91,108,92,98,177,107,92,277,107,127,207,211,96,111,216,93,59,69,87,100,91,72,95,83,105,235,65,67,77,115,83,88,67,80,70,75,129,86,103,93,71,78,66,87,59,103,119,106,128,62,93,103,122,70,91,110,59,67,77,103,177,277,303,99,73,89,72,117,116,84,71,70,85,84,85,138,142,67,71,63,187,70,57,75,74,76,80,117,118,104,86,77,88,68,161", "endOffsets": "17447,17538,17620,23333,25246,25328,25409,27516,28710,28863,28956,31576,33351,33527,33716,34162,34394,34527,34774,34983,35171,35270,35448,35556,35649,35927,36035,36163,36371,36583,36680,36891,37108,37202,37262,37332,37420,37521,37613,37686,37782,37866,37972,38208,38274,38342,38420,38536,38620,38709,38777,38858,38929,39005,39135,39654,39758,39852,39924,40003,40070,40158,40218,40322,40442,40549,40678,40741,40835,40939,41062,41133,41454,41795,43438,43506,43584,43688,43866,44144,44448,44548,44622,45269,45342,45460,45577,45662,45734,45805,45891,45976,46062,46201,46344,46412,46484,46633,46821,46892,46950,47026,47101,47178,47259,47377,47496,47601,47688,47766,47855,47924,48086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,477,646,731", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "169,256,336,472,641,726,805"}, "to": {"startLines": "92,100,171,175,498,504,505", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9456,10110,15794,16091,48091,48699,48784", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "9520,10192,15869,16222,48255,48779,48858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,205", "endColumns": "77,71,76", "endOffsets": "128,200,277"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4642,9823,10197", "endColumns": "77,71,76", "endOffsets": "4715,9890,10269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,344,447,688,736,806,907,980,1240,1300,1391,1457,1512,1576,1652,1744,2053,2125,2192,2245,2298,2388,2521,2629,2686,2772,2853,2939,3018,3124,3437,3516,3593,3657,3717,3779,3839,3912,3994,4094,4189,4288,4388,4482,4556,4635,4720,4949,5187,5303,5435,5493,6247,6351,6413", "endColumns": "119,168,102,240,47,69,100,72,259,59,90,65,54,63,75,91,308,71,66,52,52,89,132,107,56,85,80,85,78,105,312,78,76,63,59,61,59,72,81,99,94,98,99,93,73,78,84,228,237,115,131,57,753,103,61,64", "endOffsets": "170,339,442,683,731,801,902,975,1235,1295,1386,1452,1507,1571,1647,1739,2048,2120,2187,2240,2293,2383,2516,2624,2681,2767,2848,2934,3013,3119,3432,3511,3588,3652,3712,3774,3834,3907,3989,4089,4184,4283,4383,4477,4551,4630,4715,4944,5182,5298,5430,5488,6242,6346,6408,6473"}, "to": {"startLines": "260,261,262,264,268,269,270,271,272,273,274,290,293,295,299,300,305,311,312,320,330,334,335,336,337,338,344,347,352,354,355,356,357,360,362,363,367,371,372,374,376,388,413,414,415,416,417,435,442,443,444,445,447,448,449,466", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22846,22966,23135,23338,24457,24505,24575,24676,24749,25009,25069,27074,27323,27521,27819,27895,28259,28961,29033,29567,30498,30796,30886,31019,31127,31184,31757,32008,32460,32615,32721,33034,33113,33356,33532,33592,33897,34532,34605,34779,34988,36685,39140,39240,39334,39408,39487,41138,41854,42092,42208,42340,42463,43217,43321,45119", "endColumns": "119,168,102,240,47,69,100,72,259,59,90,65,54,63,75,91,308,71,66,52,52,89,132,107,56,85,80,85,78,105,312,78,76,63,59,61,59,72,81,99,94,98,99,93,73,78,84,228,237,115,131,57,753,103,61,64", "endOffsets": "22961,23130,23233,23574,24500,24570,24671,24744,25004,25064,25155,27135,27373,27580,27890,27982,28563,29028,29095,29615,30546,30881,31014,31122,31179,31265,31833,32089,32534,32716,33029,33108,33185,33415,33587,33649,33952,34600,34682,34874,35078,36779,39235,39329,39403,39482,39567,41362,42087,42203,42335,42393,43212,43316,43378,45179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,200,267,351,420,481,552,618,680,748,818,878,940,1013,1077,1154,1217,1290,1353,1444,1519,1601,1693,1791,1876,1923,1970,2046,2110,2176,2245,2349,2436,2534", "endColumns": "63,80,66,83,68,60,70,65,61,67,69,59,61,72,63,76,62,72,62,90,74,81,91,97,84,46,46,75,63,65,68,103,86,97,96", "endOffsets": "114,195,262,346,415,476,547,613,675,743,813,873,935,1008,1072,1149,1212,1285,1348,1439,1514,1596,1688,1786,1871,1918,1965,2041,2105,2171,2240,2344,2431,2529,2626"}, "to": {"startLines": "198,201,204,206,207,208,215,216,218,219,220,221,222,223,224,226,227,230,241,242,254,256,257,291,292,306,318,319,321,328,329,339,340,348,349", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17920,18166,18432,18577,18661,18730,19226,19297,19435,19497,19565,19635,19695,19757,19830,19953,20030,20265,21182,21245,22282,22443,22525,27140,27238,28568,29444,29491,29620,30363,30429,31270,31374,32094,32192", "endColumns": "63,80,66,83,68,60,70,65,61,67,69,59,61,72,63,76,62,72,62,90,74,81,91,97,84,46,46,75,63,65,68,103,86,97,96", "endOffsets": "17979,18242,18494,18656,18725,18786,19292,19358,19492,19560,19630,19690,19752,19825,19889,20025,20088,20333,21240,21331,22352,22520,22612,27233,27318,28610,29486,29562,29679,30424,30493,31369,31456,32187,32284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,195,259,346,409,481,540,616,686,753,822", "endColumns": "80,58,63,86,62,71,58,75,69,66,68,66", "endOffsets": "131,190,254,341,404,476,535,611,681,748,817,884"}, "to": {"startLines": "199,209,211,212,213,217,225,228,231,235,239,243", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17984,18791,18929,18993,19080,19363,19894,20093,20338,20652,21024,21336", "endColumns": "80,58,63,86,62,71,58,75,69,66,68,66", "endOffsets": "18060,18845,18988,19075,19138,19430,19948,20164,20403,20714,21088,21398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d75c5a854b2b1764f3caa76d8e9ef40e\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "96", "endOffsets": "147"}, "to": {"startLines": "333", "startColumns": "4", "startOffsets": "30699", "endColumns": "96", "endOffsets": "30791"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,89", "endOffsets": "136,226"}, "to": {"startLines": "502,503", "startColumns": "4,4", "startOffsets": "48523,48609", "endColumns": "85,89", "endOffsets": "48604,48694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,341,427,485,544,600,676,759,839,940,1041,1125,1203,1282,1365,1461,1551,1618,1705,1792,1891,2010,2099,2186,2264,2364,2440,2533,2623,2709,2793,2905,2978,3064,3182,3293,3368,3432,4171,4886,4963,5085,5189,5244,5344,5439,5505,5597,5690,5747,5831,5882,5966,6065,6133,6204,6271,6337,6384,6465,6560,6635,6683,6728,6805,6863,6929,7113,7287,7407,7472,7555,7633,7731,7816,7901,7978,8072,8148,8254,8341,8430,8483,8612,8660,8714,8779,8854,8922,8990,9064,9152,9220,9271", "endColumns": "68,77,61,76,85,57,58,55,75,82,79,100,100,83,77,78,82,95,89,66,86,86,98,118,88,86,77,99,75,92,89,85,83,111,72,85,117,110,74,63,738,714,76,121,103,54,99,94,65,91,92,56,83,50,83,98,67,70,66,65,46,80,94,74,47,44,76,57,65,183,173,119,64,82,77,97,84,84,76,93,75,105,86,88,52,128,47,53,64,74,67,67,73,87,67,50,84", "endOffsets": "119,197,259,336,422,480,539,595,671,754,834,935,1036,1120,1198,1277,1360,1456,1546,1613,1700,1787,1886,2005,2094,2181,2259,2359,2435,2528,2618,2704,2788,2900,2973,3059,3177,3288,3363,3427,4166,4881,4958,5080,5184,5239,5339,5434,5500,5592,5685,5742,5826,5877,5961,6060,6128,6199,6266,6332,6379,6460,6555,6630,6678,6723,6800,6858,6924,7108,7282,7402,7467,7550,7628,7726,7811,7896,7973,8067,8143,8249,8336,8425,8478,8607,8655,8709,8774,8849,8917,8985,9059,9147,9215,9266,9351"}, "to": {"startLines": "184,185,186,187,188,189,190,194,195,196,197,200,202,203,205,210,214,229,232,233,234,236,237,238,240,244,245,246,247,248,249,250,251,252,253,255,258,259,265,266,267,278,279,280,281,282,283,284,285,286,287,288,289,296,297,298,301,302,303,304,308,313,314,315,316,317,322,323,324,325,326,327,331,332,342,343,345,346,350,351,353,358,365,366,437,438,439,441,446,459,460,461,462,463,464,465,481", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16887,16956,17034,17096,17173,17259,17317,17625,17681,17757,17840,18065,18247,18348,18499,18850,19143,20169,20408,20498,20565,20719,20806,20905,21093,21403,21490,21568,21668,21744,21837,21927,22013,22097,22209,22357,22617,22735,23579,23654,23718,25414,26129,26206,26328,26432,26487,26587,26682,26748,26840,26933,26990,27585,27636,27720,27987,28055,28126,28193,28715,29100,29181,29276,29351,29399,29684,29761,29819,29885,30069,30243,30551,30616,31581,31659,31838,31923,32289,32366,32539,33190,33721,33808,41459,41512,41641,41800,42398,44627,44702,44770,44838,44912,45000,45068,46489", "endColumns": "68,77,61,76,85,57,58,55,75,82,79,100,100,83,77,78,82,95,89,66,86,86,98,118,88,86,77,99,75,92,89,85,83,111,72,85,117,110,74,63,738,714,76,121,103,54,99,94,65,91,92,56,83,50,83,98,67,70,66,65,46,80,94,74,47,44,76,57,65,183,173,119,64,82,77,97,84,84,76,93,75,105,86,88,52,128,47,53,64,74,67,67,73,87,67,50,84", "endOffsets": "16951,17029,17091,17168,17254,17312,17371,17676,17752,17835,17915,18161,18343,18427,18572,18924,19221,20260,20493,20560,20647,20801,20900,21019,21177,21485,21563,21663,21739,21832,21922,22008,22092,22204,22277,22438,22730,22841,23649,23713,24452,26124,26201,26323,26427,26482,26582,26677,26743,26835,26928,26985,27069,27631,27715,27814,28050,28121,28188,28254,28757,29176,29271,29346,29394,29439,29756,29814,29880,30064,30238,30358,30611,30694,31654,31752,31918,32003,32361,32455,32610,33291,33803,33892,41507,41636,41684,41849,42458,44697,44765,44833,44907,44995,45063,45114,46569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "106,506", "startColumns": "4,4", "startOffsets": "10676,48863", "endColumns": "60,77", "endOffsets": "10732,48936"}}]}]}