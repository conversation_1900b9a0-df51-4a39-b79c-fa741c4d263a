1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="me.liolin.app_badge_plus" >
4
5    <uses-sdk android:minSdkVersion="19" />
6
7    <!-- Samsung -->
8    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
8-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:5:5-86
8-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:5:22-83
9    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" />
9-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:6:5-87
9-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:6:22-84
10
11    <!-- HTC -->
12    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
12-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:9:5-81
12-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:9:22-78
13    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" />
13-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:10:5-83
13-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:10:22-80
14
15    <!-- Sony -->
16    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
16-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:13:5-88
16-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:13:22-85
17    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" />
17-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:14:5-92
17-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:14:22-89
18
19    <!-- Apex -->
20    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" />
20-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:17:5-84
20-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:17:22-81
21
22    <!-- Solid -->
23    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" />
23-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:20:5-83
23-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:20:22-80
24
25    <!-- Huawei -->
26    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
26-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:23:5-91
26-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:23:22-88
27    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
27-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:24:5-92
27-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:24:22-89
28    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />
28-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:25:5-93
28-->C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\app_badge_plus-1.2.3\android\src\main\AndroidManifest.xml:25:22-90
29
30</manifest>
