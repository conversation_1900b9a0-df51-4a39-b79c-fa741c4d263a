{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-108:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1337,1403,1524"}, "to": {"startLines": "48,49,75,76,78,89,90,150,151,153,154,157,158,162,486,487,488", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4397,4489,7477,7571,7744,8642,8724,13367,13456,13614,13679,13955,14033,14354,44916,44993,45059", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "4484,4566,7566,7665,7826,8719,8808,13451,13535,13674,13738,14028,14110,14422,44988,45054,45175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b04a7b60dccd9124e74d23c94f4b7089\\transformed\\jetified-facebook-login-18.0.3\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,190,302,410", "endColumns": "134,111,107,147", "endOffsets": "185,297,405,553"}, "to": {"startLines": "51,52,53,54", "startColumns": "4,4,4,4", "startOffsets": "4649,4784,4896,5004", "endColumns": "134,111,107,147", "endOffsets": "4779,4891,4999,5147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,266,348,417,478,549,616,678,746,816,876,939,1013,1077,1153,1216,1288,1351,1437,1514,1595,1689,1785,1867,1916,1963,2038,2102,2169,2238,2340,2423,2521", "endColumns": "64,77,67,81,68,60,70,66,61,67,69,59,62,73,63,75,62,71,62,85,76,80,93,95,81,48,46,74,63,66,68,101,82,97,95", "endOffsets": "115,193,261,343,412,473,544,611,673,741,811,871,934,1008,1072,1148,1211,1283,1346,1432,1509,1590,1684,1780,1862,1911,1958,2033,2097,2164,2233,2335,2418,2516,2612"}, "to": {"startLines": "186,189,192,194,195,196,203,204,206,207,208,209,210,211,212,214,215,218,229,230,242,244,245,279,280,294,306,307,309,316,317,326,327,335,336", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16119,16360,16622,16768,16850,16919,17414,17485,17621,17683,17751,17821,17881,17944,18018,18141,18217,18450,19335,19398,20445,20604,20685,25015,25111,26399,27258,27305,27433,28159,28226,28966,29068,29766,29864", "endColumns": "64,77,67,81,68,60,70,66,61,67,69,59,62,73,63,75,62,71,62,85,76,80,93,95,81,48,46,74,63,66,68,101,82,97,95", "endOffsets": "16179,16433,16685,16845,16914,16975,17480,17547,17678,17746,17816,17876,17939,18013,18077,18212,18275,18517,19393,19479,20517,20680,20774,25106,25188,26443,27300,27375,27492,28221,28290,29063,29146,29859,29955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "73,81,152,156,485,491,492", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7311,7956,13540,13821,44747,45351,45431", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "7374,8038,13609,13950,44911,45426,45502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "74,84,85,86", "startColumns": "4,4,4,4", "startOffsets": "7379,8209,8306,8415", "endColumns": "97,96,108,98", "endOffsets": "7472,8301,8410,8509"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,163,322,420,617,662,732,833,905,1150,1210,1301,1368,1423,1487,1561,1651,1935,2010,2076,2129,2182,2269,2398,2507,2564,2650,2730,2813,2878,2972,3243,3317,3392,3453,3513,3573,3633,3699,3773,3874,3962,4046,4144,4236,4309,4388,4473,4658,4852,4959,5080,5135,5841,5936,5997", "endColumns": "107,158,97,196,44,69,100,71,244,59,90,66,54,63,73,89,283,74,65,52,52,86,128,108,56,85,79,82,64,93,270,73,74,60,59,59,59,65,73,100,87,83,97,91,72,78,84,184,193,106,120,54,705,94,60,54", "endOffsets": "158,317,415,612,657,727,828,900,1145,1205,1296,1363,1418,1482,1556,1646,1930,2005,2071,2124,2177,2264,2393,2502,2559,2645,2725,2808,2873,2967,3238,3312,3387,3448,3508,3568,3628,3694,3768,3869,3957,4041,4139,4231,4304,4383,4468,4653,4847,4954,5075,5130,5836,5931,5992,6047"}, "to": {"startLines": "248,249,250,252,256,257,258,259,260,261,262,278,281,283,287,288,293,299,300,308,318,321,322,323,324,325,331,334,339,341,342,343,344,347,349,350,354,358,359,361,363,375,400,401,402,403,404,422,429,430,431,432,434,435,436,453", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20979,21087,21246,21432,22436,22481,22551,22652,22724,22969,23029,24948,25193,25380,25671,25745,26115,26778,26853,27380,28295,28498,28585,28714,28823,28880,29433,29683,30125,30266,30360,30631,30705,30936,31110,31170,31459,32017,32083,32243,32446,34059,36336,36434,36526,36599,36678,38284,38932,39126,39233,39354,39476,40182,40277,41952", "endColumns": "107,158,97,196,44,69,100,71,244,59,90,66,54,63,73,89,283,74,65,52,52,86,128,108,56,85,79,82,64,93,270,73,74,60,59,59,59,65,73,100,87,83,97,91,72,78,84,184,193,106,120,54,705,94,60,54", "endOffsets": "21082,21241,21339,21624,22476,22546,22647,22719,22964,23024,23115,25010,25243,25439,25740,25830,26394,26848,26914,27428,28343,28580,28709,28818,28875,28961,29508,29761,30185,30355,30626,30700,30775,30992,31165,31225,31514,32078,32152,32339,32529,34138,36429,36521,36594,36673,36758,38464,39121,39228,39349,39404,40177,40272,40333,42002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,777,892,971,1031,1096,1186,1253,1312,1402,1466,1530,1593,1662,1726,1780,1892,1950,2012,2066,2138,2260,2347,2422,2513,2594,2675,2815,2892,2973,3100,3191,3268,3322,3373,3439,3509,3586,3657,3732,3803,3880,3949,4018,4125,4216,4288,4377,4466,4540,4612,4698,4748,4827,4893,4973,5057,5119,5183,5246,5315,5415,5510,5602,5694,5752,5807,5885,5966,6041", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "267,349,427,504,590,674,772,887,966,1026,1091,1181,1248,1307,1397,1461,1525,1588,1657,1721,1775,1887,1945,2007,2061,2133,2255,2342,2417,2508,2589,2670,2810,2887,2968,3095,3186,3263,3317,3368,3434,3504,3581,3652,3727,3798,3875,3944,4013,4120,4211,4283,4372,4461,4535,4607,4693,4743,4822,4888,4968,5052,5114,5178,5241,5310,5410,5505,5597,5689,5747,5802,5880,5961,6036,6111"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,79,80,83,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,155,160,161,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2979,3061,3139,3216,3302,4105,4203,4318,7831,7891,8119,8575,8813,8872,8962,9026,9090,9153,9222,9286,9340,9452,9510,9572,9626,9698,9820,9907,9982,10073,10154,10235,10375,10452,10533,10660,10751,10828,10882,10933,10999,11069,11146,11217,11292,11363,11440,11509,11578,11685,11776,11848,11937,12026,12100,12172,12258,12308,12387,12453,12533,12617,12679,12743,12806,12875,12975,13070,13162,13254,13312,13743,14198,14279,14427", "endLines": "5,33,34,35,36,37,45,46,47,79,80,83,88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,155,160,161,163", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "317,3056,3134,3211,3297,3381,4198,4313,4392,7886,7951,8204,8637,8867,8957,9021,9085,9148,9217,9281,9335,9447,9505,9567,9621,9693,9815,9902,9977,10068,10149,10230,10370,10447,10528,10655,10746,10823,10877,10928,10994,11064,11141,11212,11287,11358,11435,11504,11573,11680,11771,11843,11932,12021,12095,12167,12253,12303,12382,12448,12528,12612,12674,12738,12801,12870,12970,13065,13157,13249,13307,13362,13816,14274,14349,14497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,207", "endColumns": "77,73,75", "endOffsets": "128,202,278"}, "to": {"startLines": "50,77,82", "startColumns": "4,4,4", "startOffsets": "4571,7670,8043", "endColumns": "77,73,75", "endOffsets": "4644,7739,8114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,70", "endOffsets": "262,333"}, "to": {"startLines": "87,493", "startColumns": "4,4", "startOffsets": "8514,45507", "endColumns": "60,74", "endOffsets": "8570,45577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\********************************\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,137,235,301,369,433,506", "endColumns": "81,97,65,67,63,72,67", "endOffsets": "132,230,296,364,428,501,569"}, "to": {"startLines": "165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14603,14685,14783,14849,14917,14981,15054", "endColumns": "81,97,65,67,63,72,67", "endOffsets": "14680,14778,14844,14912,14976,15049,15117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,426,526,634,718,818,933,1011,1086,1177,1270,1365,1459,1559,1652,1747,1841,1932,2023,2105,2208,2311,2410,2515,2619,2723,2879,14115", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "421,521,629,713,813,928,1006,1081,1172,1265,1360,1454,1554,1647,1742,1836,1927,2018,2100,2203,2306,2405,2510,2614,2718,2874,2974,14193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "133", "endOffsets": "332"}, "to": {"startLines": "63", "startColumns": "4", "startOffsets": "6134", "endColumns": "137", "endOffsets": "6267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,207,282,370,454,535,615,747,835,938,1030,1140,1199,1312,1375,1543,1727,1873,1959,2061,2153,2251,2421,2522,2618,2856,2967,3094,3283,3496,3586,3697,3879,3973,4033,4097,4179,4273,4364,4436,4530,4614,4718,4913,4979,5045,5119,5230,5304,5388,5450,5526,5595,5663,5779,5865,5969,6063,6135,6214,6282,6368,6426,6526,6634,6732,6852,6912,7004,7106,7229,7300,7383,7481,7536,7589,7665,7762,7917,8168,8444,8548,8625,8706,8776,8883,9002,9085,9159,9227,9307,9391,9482,9587,9700,9766,9833,9888,10048,10116,10174,10254,10327,10405,10479,10598,10721,10831,10917,10989,11073,11139", "endColumns": "68,82,74,87,83,80,79,131,87,102,91,109,58,112,62,167,183,145,85,101,91,97,169,100,95,237,110,126,188,212,89,110,181,93,59,63,81,93,90,71,93,83,103,194,65,65,73,110,73,83,61,75,68,67,115,85,103,93,71,78,67,85,57,99,107,97,119,59,91,101,122,70,82,97,54,52,75,96,154,250,275,103,76,80,69,106,118,82,73,67,79,83,90,104,112,65,66,54,159,67,57,79,72,77,73,118,122,109,85,71,83,65,150", "endOffsets": "119,202,277,365,449,530,610,742,830,933,1025,1135,1194,1307,1370,1538,1722,1868,1954,2056,2148,2246,2416,2517,2613,2851,2962,3089,3278,3491,3581,3692,3874,3968,4028,4092,4174,4268,4359,4431,4525,4609,4713,4908,4974,5040,5114,5225,5299,5383,5445,5521,5590,5658,5774,5860,5964,6058,6130,6209,6277,6363,6421,6521,6629,6727,6847,6907,6999,7101,7224,7295,7378,7476,7531,7584,7660,7757,7912,8163,8439,8543,8620,8701,8771,8878,8997,9080,9154,9222,9302,9386,9477,9582,9695,9761,9828,9883,10043,10111,10169,10249,10322,10400,10474,10593,10716,10826,10912,10984,11068,11134,11285"}, "to": {"startLines": "179,180,181,251,263,264,265,282,295,297,298,328,346,348,351,355,356,357,360,362,364,365,366,367,368,369,370,371,372,373,374,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,423,427,437,438,439,440,441,442,443,444,445,454,455,456,457,458,459,460,461,462,463,464,465,466,467,469,470,471,472,473,474,475,476,477,478,479,480,481,482,483,484", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15595,15664,15747,21344,23120,23204,23285,25248,26448,26583,26686,29151,30877,30997,31230,31519,31687,31871,32157,32344,32534,32626,32724,32894,32995,33091,33329,33440,33567,33756,33969,34143,34254,34436,34530,34590,34654,34736,34830,34921,34993,35087,35171,35275,35470,35536,35602,35676,35787,35861,35945,36007,36083,36152,36220,36763,36849,36953,37047,37119,37198,37266,37352,37410,37510,37618,37716,37836,37896,37988,38090,38213,38469,38780,40338,40393,40446,40522,40619,40774,41025,41301,41405,42007,42088,42158,42265,42384,42467,42541,42609,42689,42773,42864,42969,43082,43148,43290,43345,43505,43573,43631,43711,43784,43862,43936,44055,44178,44288,44374,44446,44530,44596", "endColumns": "68,82,74,87,83,80,79,131,87,102,91,109,58,112,62,167,183,145,85,101,91,97,169,100,95,237,110,126,188,212,89,110,181,93,59,63,81,93,90,71,93,83,103,194,65,65,73,110,73,83,61,75,68,67,115,85,103,93,71,78,67,85,57,99,107,97,119,59,91,101,122,70,82,97,54,52,75,96,154,250,275,103,76,80,69,106,118,82,73,67,79,83,90,104,112,65,66,54,159,67,57,79,72,77,73,118,122,109,85,71,83,65,150", "endOffsets": "15659,15742,15817,21427,23199,23280,23360,25375,26531,26681,26773,29256,30931,31105,31288,31682,31866,32012,32238,32441,32621,32719,32889,32990,33086,33324,33435,33562,33751,33964,34054,34249,34431,34525,34585,34649,34731,34825,34916,34988,35082,35166,35270,35465,35531,35597,35671,35782,35856,35940,36002,36078,36147,36215,36331,36844,36948,37042,37114,37193,37261,37347,37405,37505,37613,37711,37831,37891,37983,38085,38208,38279,38547,38873,40388,40441,40517,40614,40769,41020,41296,41400,41477,42083,42153,42260,42379,42462,42536,42604,42684,42768,42859,42964,43077,43143,43210,43340,43500,43568,43626,43706,43779,43857,43931,44050,44173,44283,44369,44441,44525,44591,44742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,257,342,405,474,533,608,678,745,806", "endColumns": "77,58,64,84,62,68,58,74,69,66,60,66", "endOffsets": "128,187,252,337,400,469,528,603,673,740,801,868"}, "to": {"startLines": "187,197,199,200,201,205,213,216,219,223,227,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16184,16980,17118,17183,17268,17552,18082,18280,18522,18839,19193,19484", "endColumns": "77,58,64,84,62,68,58,74,69,66,60,66", "endOffsets": "16257,17034,17178,17263,17326,17616,18136,18350,18587,18901,19249,19546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,164", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3386,3482,3584,3683,3782,3886,3989,14502", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3477,3579,3678,3777,3881,3984,4100,14598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,333,411,469,528,578,658,741,825,923,1021,1107,1185,1264,1347,1442,1535,1602,1689,1776,1866,1976,2057,2144,2225,2328,2408,2504,2593,2678,2766,2873,2951,3033,3137,3233,3305,3370,4040,4686,4763,4882,4986,5041,5138,5229,5296,5388,5482,5539,5623,5672,5751,5850,5927,5997,6064,6130,6177,6257,6350,6426,6471,6516,6586,6644,6705,6882,7054,7178,7243,7328,7406,7500,7584,7670,7746,7835,7911,8008,8085,8174,8225,8353,8402,8456,8523,8586,8654,8721,8792,8879,8944,8993", "endColumns": "68,77,61,68,77,57,58,49,79,82,83,97,97,85,77,78,82,94,92,66,86,86,89,109,80,86,80,102,79,95,88,84,87,106,77,81,103,95,71,64,669,645,76,118,103,54,96,90,66,91,93,56,83,48,78,98,76,69,66,65,46,79,92,75,44,44,69,57,60,176,171,123,64,84,77,93,83,85,75,88,75,96,76,88,50,127,48,53,66,62,67,66,70,86,64,48,74", "endOffsets": "119,197,259,328,406,464,523,573,653,736,820,918,1016,1102,1180,1259,1342,1437,1530,1597,1684,1771,1861,1971,2052,2139,2220,2323,2403,2499,2588,2673,2761,2868,2946,3028,3132,3228,3300,3365,4035,4681,4758,4877,4981,5036,5133,5224,5291,5383,5477,5534,5618,5667,5746,5845,5922,5992,6059,6125,6172,6252,6345,6421,6466,6511,6581,6639,6700,6877,7049,7173,7238,7323,7401,7495,7579,7665,7741,7830,7906,8003,8080,8169,8220,8348,8397,8451,8518,8581,8649,8716,8787,8874,8939,8988,9063"}, "to": {"startLines": "172,173,174,175,176,177,178,182,183,184,185,188,190,191,193,198,202,217,220,221,222,224,225,226,228,232,233,234,235,236,237,238,239,240,241,243,246,247,253,254,255,266,267,268,269,270,271,272,273,274,275,276,277,284,285,286,289,290,291,292,296,301,302,303,304,305,310,311,312,313,314,315,319,320,329,330,332,333,337,338,340,345,352,353,424,425,426,428,433,446,447,448,449,450,451,452,468", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15122,15191,15269,15331,15400,15478,15536,15822,15872,15952,16035,16262,16438,16536,16690,17039,17331,18355,18592,18685,18752,18906,18993,19083,19254,19551,19638,19719,19822,19902,19998,20087,20172,20260,20367,20522,20779,20883,21629,21701,21766,23365,24011,24088,24207,24311,24366,24463,24554,24621,24713,24807,24864,25444,25493,25572,25835,25912,25982,26049,26536,26919,26999,27092,27168,27213,27497,27567,27625,27686,27863,28035,28348,28413,29261,29339,29513,29597,29960,30036,30190,30780,31293,31370,38552,38603,38731,38878,39409,41482,41545,41613,41680,41751,41838,41903,43215", "endColumns": "68,77,61,68,77,57,58,49,79,82,83,97,97,85,77,78,82,94,92,66,86,86,89,109,80,86,80,102,79,95,88,84,87,106,77,81,103,95,71,64,669,645,76,118,103,54,96,90,66,91,93,56,83,48,78,98,76,69,66,65,46,79,92,75,44,44,69,57,60,176,171,123,64,84,77,93,83,85,75,88,75,96,76,88,50,127,48,53,66,62,67,66,70,86,64,48,74", "endOffsets": "15186,15264,15326,15395,15473,15531,15590,15867,15947,16030,16114,16355,16531,16617,16763,17113,17409,18445,18680,18747,18834,18988,19078,19188,19330,19633,19714,19817,19897,19993,20082,20167,20255,20362,20440,20599,20878,20974,21696,21761,22431,24006,24083,24202,24306,24361,24458,24549,24616,24708,24802,24859,24943,25488,25567,25666,25907,25977,26044,26110,26578,26994,27087,27163,27208,27253,27562,27620,27681,27858,28030,28154,28408,28493,29334,29428,29592,29678,30031,30120,30261,30872,31365,31454,38598,38726,38775,38927,39471,41540,41608,41675,41746,41833,41898,41947,43285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "489,490", "startColumns": "4,4", "startOffsets": "45180,45266", "endColumns": "85,84", "endOffsets": "45261,45346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,446,567,670,817,936,1048,1147,1302,1403,1551,1672,1814,1958,2017,2075", "endColumns": "100,147,120,102,146,118,111,98,154,100,147,120,141,143,58,57,74", "endOffsets": "297,445,566,669,816,935,1047,1146,1301,1402,1550,1671,1813,1957,2016,2074,2149"}, "to": {"startLines": "55,56,57,58,59,60,61,62,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5152,5257,5409,5534,5641,5792,5915,6031,6272,6431,6536,6688,6813,6959,7107,7170,7232", "endColumns": "104,151,124,106,150,122,115,102,158,104,151,124,145,147,62,61,78", "endOffsets": "5252,5404,5529,5636,5787,5910,6026,6129,6426,6531,6683,6808,6954,7102,7165,7227,7306"}}]}]}