-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:18:5-50:19
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b04a7b60dccd9124e74d23c94f4b7089\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b04a7b60dccd9124e74d23c94f4b7089\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d482d10ea96caa790ae6cb180ae44fe\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d482d10ea96caa790ae6cb180ae44fe\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:razorpay_flutter] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-8:19
MERGED from [:razorpay_flutter] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-8:19
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:20:5-75:19
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:20:5-75:19
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:19:5-40:19
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:19:5-40:19
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:7:5-81:19
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:7:5-81:19
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:14:5-71:19
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:14:5-71:19
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:41:5-70:19
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:41:5-70:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:21:5-51:19
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:21:5-51:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f31e90339f9a544199ea162d3bf8d470\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f31e90339f9a544199ea162d3bf8d470\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98e987c5461daad3161f262e3ab33d58\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98e987c5461daad3161f262e3ab33d58\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3414bb0d58392dd57b885ac8530cf688\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3414bb0d58392dd57b885ac8530cf688\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d3c5cdd58b871f1af16f9eedf0e5619\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d3c5cdd58b871f1af16f9eedf0e5619\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1784705da8c5bfc8011d0b6e7a92f271\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1784705da8c5bfc8011d0b6e7a92f271\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bc6777b4bb426d2d9f1d209363fcc6\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bc6777b4bb426d2d9f1d209363fcc6\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43d8beb38e295c360b812dae16e889f9\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43d8beb38e295c360b812dae16e889f9\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adeb493533d1e577e19586a250438f7c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adeb493533d1e577e19586a250438f7c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d0a252875bcff714d4de4662178af2f\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d0a252875bcff714d4de4662178af2f\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:5:5-6:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb85252b3f20bcbf627cbd4f2b5a507b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb85252b3f20bcbf627cbd4f2b5a507b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91c499416f97818ae9fb8c712d1899c6\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91c499416f97818ae9fb8c712d1899c6\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66a3e28d8d72eb263995f5e2d6fe14a9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66a3e28d8d72eb263995f5e2d6fe14a9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e91fe311e695050154441765c20e615\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e91fe311e695050154441765c20e615\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e6ef253db239f863ebe4fe19fd59b24\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e6ef253db239f863ebe4fe19fd59b24\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a0791a6c9fa28d46b03b636d3da8b9\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a0791a6c9fa28d46b03b636d3da8b9\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e70112bbc84ddf19fc928e7dec52d465\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e70112bbc84ddf19fc928e7dec52d465\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b04a7b60dccd9124e74d23c94f4b7089\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:18-44
	android:name
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:2:1-51:12
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:2:1-51:12
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml:1:1-9:12
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml:1:1-9:12
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml:1:1-9:12
MERGED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-30:12
MERGED from [:flutter_timezone] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:stripe_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_facebook_auth] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_facebook_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b04a7b60dccd9124e74d23c94f4b7089\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d482d10ea96caa790ae6cb180ae44fe\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:razorpay_flutter] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:fluttertoast] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:geocoding_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:path_provider_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f14277eff132a0180a8d1edf83570821\transformed\webkit-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:2:1-77:12
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:9:1-42:12
MERGED from [com.stripe:stripe-android:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82f727cba5e63e8a994c91e8767d469d\transformed\jetified-stripe-android-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:2:1-83:12
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:2:1-73:12
MERGED from [com.stripe:stripe-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce085bb0fffc00796fbc2583b1fd195a\transformed\jetified-stripe-ui-core-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:payments-model:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a928c055d6d608b71ad17d53ec0a49f4\transformed\jetified-payments-model-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a02b55c0e8015c9916fce5af2c7d98d9\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5fe46e059e11d85049d645a77ee33cc\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e20bffac75b0a8afaaa12d4c0504232\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a27c23fe681a11e77336c539d052f6bb\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\847cce64c71a1f5aad8612be5cdc5778\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8956f2302268edd20cb7591f1a936c34\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef8995c4c5b3cc98db66b1d0d7247bca\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52e4c82290a606e210ec31b77486415e\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\302bf075c0a4312fa329877d44933230\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5dcaa28e4ba400f0836d31244c9259c\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a048d120d9888d6dccf02a3ef9c94ab\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348ff9c872fc23583a6c0da4f4784a0b\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8943a7a8d048d100be549106c4cd5a22\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50fe69452ad8872d7d613645e33dfd25\transformed\jetified-accompanist-systemuicontroller-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc1d0ab7786d9eeb4c98d48f70d4ac51\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06789ca2702f7bbf94b2b22bff508fe9\transformed\jetified-accompanist-themeadapter-appcompat-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bbdb16cc36b20b87d7a8d18534c9d14\transformed\jetified-accompanist-themeadapter-material-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a17e952dfc8633f0d2d6678cc61574c\transformed\jetified-accompanist-themeadapter-material3-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10278739ca06d9f1b8a212e0d6ee3d93\transformed\jetified-material3-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc8de89fe7ddbad7bc9fbd32638b20ef\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aeb2eeb3aaa706a2234a6c6a54fc66d9\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ec3ab6a2aa19fdb1f5470258c64f805\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29047b09d19f75405a375f353ac07c37\transformed\jetified-accompanist-themeadapter-core-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-flowlayout:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29051433f846078bf64d2db30637446d\transformed\jetified-accompanist-flowlayout-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5170876376ca2af8d6193ab082e945a\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\096f96cd89929e8660d10c15b011a02f\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57370393c9542545af304807eb0ea54a\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c4f53641dc3d7e8f1b0d51e51a6bc58\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b20139023d5fc741f41f4a71be29eb7e\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d24c9772bfa5b3715603fa55478cf58d\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e745151a108922dc287dbcc34857056d\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57ce7cbe2c1cd6257c415850eae7d949\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\794475784dbfad532c9e6c169287eb32\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea406c2c21a8a5cdd46e22a204cc6c11\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6146b2516dc448e86b32320efb56c1b\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:2:1-7:12
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:2:1-72:12
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:9:1-53:12
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f31e90339f9a544199ea162d3bf8d470\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98e987c5461daad3161f262e3ab33d58\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b66ef05bfa258d0baf91f31afe8023\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c367480b2717917f45d4a204905899e\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1190dd459da609aa6653301668b0003\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1431910119533f09d2e29c2b054edd9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-wallet:19.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fbae9977177ee1e6c51272f147efa70\transformed\jetified-play-services-wallet-19.4.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3414bb0d58392dd57b885ac8530cf688\transformed\jetified-glide-4.12.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d3c5cdd58b871f1af16f9eedf0e5619\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.stripe:hcaptcha:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d75c5a854b2b1764f3caa76d8e9ef40e\transformed\jetified-hcaptcha-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffd65503b5355d51d2e642ac28f77c72\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aacdcb9fcbca67a896797d8f06149192\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e90d23b1033d1be3b823d12a38ef36d1\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\739b63c0e621bf8c4a204d3eb72db3c2\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a555a22a1c5d09f2b870b6d071ca1725\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfdab1cd72495eaa827622b54cfa2259\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1784705da8c5bfc8011d0b6e7a92f271\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bc6777b4bb426d2d9f1d209363fcc6\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43d8beb38e295c360b812dae16e889f9\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adeb493533d1e577e19586a250438f7c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c4a036db10ec7cd0d359d0598a3abc7\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.stripe:attestation:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0d619382029845b988809a0cffc6468\transformed\jetified-attestation-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d0a252875bcff714d4de4662178af2f\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ca8644b6dcb915199c320064d9cab59\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5e8f4ae7dae2e73112bebf34838a42c\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffb258e58b3357165d2eb2ce2cf52fe9\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\094c3e8c0f4cc5a4d704da2ae303ba5b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dee6a0e2abb6d4f240a1fff8f0066e11\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c243b0ed722d144420e5da95f20efb0\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b29946de1704cba5117374d4f6e419e\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f8287cbf491a045aedcd30e57ef8d83\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a63ff6fe3884e6aa1e8798f0eb8e939\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25de978c550012476f2d1e4bbc64d2a1\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a01bed10a45d0be1382288751ee10690\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74fb6f5ebf2651febc6f4fc32263e4b2\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c5d88081de8146dff16cd865e92df55\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\add29a7a0581b62c87aca769f58b3520\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b5dab80b74f2aafbb15457fbc262312\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78ef0c3dddcfb008f913942ac88dbea5\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ddc69a396975651deab8c85bcb6b612\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb85252b3f20bcbf627cbd4f2b5a507b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13c029f2f58e3b165baf837536d94384\transformed\fragment-1.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78e44ac02a6e7862827ab24939099cbf\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bbaa2c40c57dd0a4ff5b0d2f5d0a3d8\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b0ebd47eda160c73b7baba5c4a8f3b2\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d31ce596ba0ed56298a00e64a3683a1\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ae0e65c4291556b5562b14122cf5f17\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d358571ce8959fc2cf602535ec5fba90\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e964f52d4e8d584d79488f5fbb8af38\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-viewbinding:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a3d4e04de5964ad7375ade1fc76414a\transformed\jetified-ui-viewbinding-1.6.8\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42ae3aaff063e14fe24bffb9620fc4a6\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a96922616c95e9effdf660ff586d850\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa81b0fd847946b4692317b44a490218\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9f0d9e661ed4cae676e87652f39382f\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91c499416f97818ae9fb8c712d1899c6\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\462554839bce9140ef711a31a410ccb8\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afcdd463b766b540cef43fabf2181a90\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9cb05d79a05ab8e359445a29d7fdd63f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed6f5543ac410a4be33632b4f11d9ab0\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e281aae9140f98d922b29e569f0a721\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c57cba69b05a28f9d6e3de14b699513\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c93b90ca948c590bb01b92589a2a0c77\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9597676f1168cbff42b9ed7269a0f1b1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be08b247c8bed180b7b9a261ee7ee494\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082e9c0b81906a4a80dfb88ad693b00\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d6126b92cc4707dc86b2365c6b1b2ab\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc57ca96e47057b35c4b876f2cfb963d\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66a3e28d8d72eb263995f5e2d6fe14a9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c34c3f5c46eeece978bfd9c7e3790dd\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53bc04b37c3169bd1bf59d0b71b12483\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e91fe311e695050154441765c20e615\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e6ef253db239f863ebe4fe19fd59b24\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e1cc558008c1c88481c9a0467b8cd48\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74a8815656e6ae477cc0ff49935ee45\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01ee4962a5ed47a8c1f3b06dbeacfd46\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aa34306e16f3bbcf7d9f2c028e6d56b\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4104d6451bda1e06c2db9cec675b5d77\transformed\jetified-viewbinding-8.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d75a5928c778e2e068179a18a2149edd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e865ac42435c71c7d834ce4071e5cb7c\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f36302c7730400571c9add63713632\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5910f0094b7e90a8cfd982b9edd9fb36\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f08243ee29d0209319f5809d2cf35d9e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54c3b60611749c741cabf3a7a812dde4\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a0791a6c9fa28d46b03b636d3da8b9\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3b0e5bf1d9235d9a613f8c5d332c501\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e70112bbc84ddf19fc928e7dec52d465\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:2:1-21:12
	package
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:3:5-29
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:7:5-67
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:7:5-67
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c4a036db10ec7cd0d359d0598a3abc7\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c4a036db10ec7cd0d359d0598a3abc7\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a02b55c0e8015c9916fce5af2c7d98d9\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:7:5-79
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a02b55c0e8015c9916fce5af2c7d98d9\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c4a036db10ec7cd0d359d0598a3abc7\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c4a036db10ec7cd0d359d0598a3abc7\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:22-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:22-63
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:5-65
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:5-80
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:5-81
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:5-76
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:22-73
uses-sdk
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
MERGED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_timezone] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_timezone] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_facebook_auth] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_facebook_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_facebook_auth] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_facebook_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b04a7b60dccd9124e74d23c94f4b7089\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b04a7b60dccd9124e74d23c94f4b7089\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d482d10ea96caa790ae6cb180ae44fe\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d482d10ea96caa790ae6cb180ae44fe\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:razorpay_flutter] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:razorpay_flutter] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f14277eff132a0180a8d1edf83570821\transformed\webkit-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f14277eff132a0180a8d1edf83570821\transformed\webkit-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:13:5-44
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:13:5-44
MERGED from [com.stripe:stripe-android:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82f727cba5e63e8a994c91e8767d469d\transformed\jetified-stripe-android-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-android:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82f727cba5e63e8a994c91e8767d469d\transformed\jetified-stripe-android-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce085bb0fffc00796fbc2583b1fd195a\transformed\jetified-stripe-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce085bb0fffc00796fbc2583b1fd195a\transformed\jetified-stripe-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a928c055d6d608b71ad17d53ec0a49f4\transformed\jetified-payments-model-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a928c055d6d608b71ad17d53ec0a49f4\transformed\jetified-payments-model-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a02b55c0e8015c9916fce5af2c7d98d9\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a02b55c0e8015c9916fce5af2c7d98d9\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5fe46e059e11d85049d645a77ee33cc\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5fe46e059e11d85049d645a77ee33cc\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e20bffac75b0a8afaaa12d4c0504232\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e20bffac75b0a8afaaa12d4c0504232\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a27c23fe681a11e77336c539d052f6bb\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a27c23fe681a11e77336c539d052f6bb\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\847cce64c71a1f5aad8612be5cdc5778\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\847cce64c71a1f5aad8612be5cdc5778\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8956f2302268edd20cb7591f1a936c34\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8956f2302268edd20cb7591f1a936c34\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef8995c4c5b3cc98db66b1d0d7247bca\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef8995c4c5b3cc98db66b1d0d7247bca\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52e4c82290a606e210ec31b77486415e\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\52e4c82290a606e210ec31b77486415e\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\302bf075c0a4312fa329877d44933230\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\302bf075c0a4312fa329877d44933230\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5dcaa28e4ba400f0836d31244c9259c\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5dcaa28e4ba400f0836d31244c9259c\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a048d120d9888d6dccf02a3ef9c94ab\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a048d120d9888d6dccf02a3ef9c94ab\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348ff9c872fc23583a6c0da4f4784a0b\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\348ff9c872fc23583a6c0da4f4784a0b\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8943a7a8d048d100be549106c4cd5a22\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8943a7a8d048d100be549106c4cd5a22\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50fe69452ad8872d7d613645e33dfd25\transformed\jetified-accompanist-systemuicontroller-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50fe69452ad8872d7d613645e33dfd25\transformed\jetified-accompanist-systemuicontroller-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc1d0ab7786d9eeb4c98d48f70d4ac51\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc1d0ab7786d9eeb4c98d48f70d4ac51\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06789ca2702f7bbf94b2b22bff508fe9\transformed\jetified-accompanist-themeadapter-appcompat-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06789ca2702f7bbf94b2b22bff508fe9\transformed\jetified-accompanist-themeadapter-appcompat-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bbdb16cc36b20b87d7a8d18534c9d14\transformed\jetified-accompanist-themeadapter-material-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bbdb16cc36b20b87d7a8d18534c9d14\transformed\jetified-accompanist-themeadapter-material-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a17e952dfc8633f0d2d6678cc61574c\transformed\jetified-accompanist-themeadapter-material3-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a17e952dfc8633f0d2d6678cc61574c\transformed\jetified-accompanist-themeadapter-material3-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10278739ca06d9f1b8a212e0d6ee3d93\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\10278739ca06d9f1b8a212e0d6ee3d93\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc8de89fe7ddbad7bc9fbd32638b20ef\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc8de89fe7ddbad7bc9fbd32638b20ef\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aeb2eeb3aaa706a2234a6c6a54fc66d9\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aeb2eeb3aaa706a2234a6c6a54fc66d9\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ec3ab6a2aa19fdb1f5470258c64f805\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ec3ab6a2aa19fdb1f5470258c64f805\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29047b09d19f75405a375f353ac07c37\transformed\jetified-accompanist-themeadapter-core-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29047b09d19f75405a375f353ac07c37\transformed\jetified-accompanist-themeadapter-core-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29051433f846078bf64d2db30637446d\transformed\jetified-accompanist-flowlayout-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29051433f846078bf64d2db30637446d\transformed\jetified-accompanist-flowlayout-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5170876376ca2af8d6193ab082e945a\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5170876376ca2af8d6193ab082e945a\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\096f96cd89929e8660d10c15b011a02f\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\096f96cd89929e8660d10c15b011a02f\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57370393c9542545af304807eb0ea54a\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57370393c9542545af304807eb0ea54a\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c4f53641dc3d7e8f1b0d51e51a6bc58\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c4f53641dc3d7e8f1b0d51e51a6bc58\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b20139023d5fc741f41f4a71be29eb7e\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b20139023d5fc741f41f4a71be29eb7e\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d24c9772bfa5b3715603fa55478cf58d\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d24c9772bfa5b3715603fa55478cf58d\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e745151a108922dc287dbcc34857056d\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e745151a108922dc287dbcc34857056d\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57ce7cbe2c1cd6257c415850eae7d949\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57ce7cbe2c1cd6257c415850eae7d949\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\794475784dbfad532c9e6c169287eb32\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\794475784dbfad532c9e6c169287eb32\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea406c2c21a8a5cdd46e22a204cc6c11\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea406c2c21a8a5cdd46e22a204cc6c11\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6146b2516dc448e86b32320efb56c1b\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f6146b2516dc448e86b32320efb56c1b\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:6:5-44
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f31e90339f9a544199ea162d3bf8d470\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f31e90339f9a544199ea162d3bf8d470\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98e987c5461daad3161f262e3ab33d58\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98e987c5461daad3161f262e3ab33d58\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b66ef05bfa258d0baf91f31afe8023\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\97b66ef05bfa258d0baf91f31afe8023\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c367480b2717917f45d4a204905899e\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2c367480b2717917f45d4a204905899e\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1190dd459da609aa6653301668b0003\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f1190dd459da609aa6653301668b0003\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1431910119533f09d2e29c2b054edd9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1431910119533f09d2e29c2b054edd9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fbae9977177ee1e6c51272f147efa70\transformed\jetified-play-services-wallet-19.4.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fbae9977177ee1e6c51272f147efa70\transformed\jetified-play-services-wallet-19.4.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3414bb0d58392dd57b885ac8530cf688\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3414bb0d58392dd57b885ac8530cf688\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d3c5cdd58b871f1af16f9eedf0e5619\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d3c5cdd58b871f1af16f9eedf0e5619\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:hcaptcha:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d75c5a854b2b1764f3caa76d8e9ef40e\transformed\jetified-hcaptcha-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:hcaptcha:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d75c5a854b2b1764f3caa76d8e9ef40e\transformed\jetified-hcaptcha-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffd65503b5355d51d2e642ac28f77c72\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffd65503b5355d51d2e642ac28f77c72\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aacdcb9fcbca67a896797d8f06149192\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aacdcb9fcbca67a896797d8f06149192\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e90d23b1033d1be3b823d12a38ef36d1\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e90d23b1033d1be3b823d12a38ef36d1\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\739b63c0e621bf8c4a204d3eb72db3c2\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\739b63c0e621bf8c4a204d3eb72db3c2\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a555a22a1c5d09f2b870b6d071ca1725\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a555a22a1c5d09f2b870b6d071ca1725\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfdab1cd72495eaa827622b54cfa2259\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cfdab1cd72495eaa827622b54cfa2259\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1784705da8c5bfc8011d0b6e7a92f271\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1784705da8c5bfc8011d0b6e7a92f271\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bc6777b4bb426d2d9f1d209363fcc6\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35bc6777b4bb426d2d9f1d209363fcc6\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43d8beb38e295c360b812dae16e889f9\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43d8beb38e295c360b812dae16e889f9\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adeb493533d1e577e19586a250438f7c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adeb493533d1e577e19586a250438f7c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c4a036db10ec7cd0d359d0598a3abc7\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c4a036db10ec7cd0d359d0598a3abc7\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:attestation:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0d619382029845b988809a0cffc6468\transformed\jetified-attestation-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:attestation:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c0d619382029845b988809a0cffc6468\transformed\jetified-attestation-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d0a252875bcff714d4de4662178af2f\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d0a252875bcff714d4de4662178af2f\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:4:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ca8644b6dcb915199c320064d9cab59\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ca8644b6dcb915199c320064d9cab59\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5e8f4ae7dae2e73112bebf34838a42c\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f5e8f4ae7dae2e73112bebf34838a42c\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffb258e58b3357165d2eb2ce2cf52fe9\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffb258e58b3357165d2eb2ce2cf52fe9\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\094c3e8c0f4cc5a4d704da2ae303ba5b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\094c3e8c0f4cc5a4d704da2ae303ba5b\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dee6a0e2abb6d4f240a1fff8f0066e11\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dee6a0e2abb6d4f240a1fff8f0066e11\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c243b0ed722d144420e5da95f20efb0\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c243b0ed722d144420e5da95f20efb0\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b29946de1704cba5117374d4f6e419e\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b29946de1704cba5117374d4f6e419e\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f8287cbf491a045aedcd30e57ef8d83\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f8287cbf491a045aedcd30e57ef8d83\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a63ff6fe3884e6aa1e8798f0eb8e939\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0a63ff6fe3884e6aa1e8798f0eb8e939\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25de978c550012476f2d1e4bbc64d2a1\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\25de978c550012476f2d1e4bbc64d2a1\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a01bed10a45d0be1382288751ee10690\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a01bed10a45d0be1382288751ee10690\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74fb6f5ebf2651febc6f4fc32263e4b2\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74fb6f5ebf2651febc6f4fc32263e4b2\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c5d88081de8146dff16cd865e92df55\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c5d88081de8146dff16cd865e92df55\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\add29a7a0581b62c87aca769f58b3520\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\add29a7a0581b62c87aca769f58b3520\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b5dab80b74f2aafbb15457fbc262312\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8b5dab80b74f2aafbb15457fbc262312\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78ef0c3dddcfb008f913942ac88dbea5\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78ef0c3dddcfb008f913942ac88dbea5\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ddc69a396975651deab8c85bcb6b612\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ddc69a396975651deab8c85bcb6b612\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb85252b3f20bcbf627cbd4f2b5a507b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb85252b3f20bcbf627cbd4f2b5a507b\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13c029f2f58e3b165baf837536d94384\transformed\fragment-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13c029f2f58e3b165baf837536d94384\transformed\fragment-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78e44ac02a6e7862827ab24939099cbf\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78e44ac02a6e7862827ab24939099cbf\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bbaa2c40c57dd0a4ff5b0d2f5d0a3d8\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bbaa2c40c57dd0a4ff5b0d2f5d0a3d8\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b0ebd47eda160c73b7baba5c4a8f3b2\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b0ebd47eda160c73b7baba5c4a8f3b2\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d31ce596ba0ed56298a00e64a3683a1\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d31ce596ba0ed56298a00e64a3683a1\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ae0e65c4291556b5562b14122cf5f17\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ae0e65c4291556b5562b14122cf5f17\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d358571ce8959fc2cf602535ec5fba90\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d358571ce8959fc2cf602535ec5fba90\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e964f52d4e8d584d79488f5fbb8af38\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e964f52d4e8d584d79488f5fbb8af38\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a3d4e04de5964ad7375ade1fc76414a\transformed\jetified-ui-viewbinding-1.6.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a3d4e04de5964ad7375ade1fc76414a\transformed\jetified-ui-viewbinding-1.6.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42ae3aaff063e14fe24bffb9620fc4a6\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\42ae3aaff063e14fe24bffb9620fc4a6\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a96922616c95e9effdf660ff586d850\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a96922616c95e9effdf660ff586d850\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa81b0fd847946b4692317b44a490218\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa81b0fd847946b4692317b44a490218\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9f0d9e661ed4cae676e87652f39382f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9f0d9e661ed4cae676e87652f39382f\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91c499416f97818ae9fb8c712d1899c6\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\91c499416f97818ae9fb8c712d1899c6\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\462554839bce9140ef711a31a410ccb8\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\462554839bce9140ef711a31a410ccb8\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afcdd463b766b540cef43fabf2181a90\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afcdd463b766b540cef43fabf2181a90\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9cb05d79a05ab8e359445a29d7fdd63f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9cb05d79a05ab8e359445a29d7fdd63f\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed6f5543ac410a4be33632b4f11d9ab0\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed6f5543ac410a4be33632b4f11d9ab0\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e281aae9140f98d922b29e569f0a721\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e281aae9140f98d922b29e569f0a721\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c57cba69b05a28f9d6e3de14b699513\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c57cba69b05a28f9d6e3de14b699513\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c93b90ca948c590bb01b92589a2a0c77\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c93b90ca948c590bb01b92589a2a0c77\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9597676f1168cbff42b9ed7269a0f1b1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9597676f1168cbff42b9ed7269a0f1b1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be08b247c8bed180b7b9a261ee7ee494\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\be08b247c8bed180b7b9a261ee7ee494\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082e9c0b81906a4a80dfb88ad693b00\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6082e9c0b81906a4a80dfb88ad693b00\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d6126b92cc4707dc86b2365c6b1b2ab\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d6126b92cc4707dc86b2365c6b1b2ab\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc57ca96e47057b35c4b876f2cfb963d\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc57ca96e47057b35c4b876f2cfb963d\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66a3e28d8d72eb263995f5e2d6fe14a9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66a3e28d8d72eb263995f5e2d6fe14a9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c34c3f5c46eeece978bfd9c7e3790dd\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c34c3f5c46eeece978bfd9c7e3790dd\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53bc04b37c3169bd1bf59d0b71b12483\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53bc04b37c3169bd1bf59d0b71b12483\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e91fe311e695050154441765c20e615\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1e91fe311e695050154441765c20e615\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e6ef253db239f863ebe4fe19fd59b24\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e6ef253db239f863ebe4fe19fd59b24\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e1cc558008c1c88481c9a0467b8cd48\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e1cc558008c1c88481c9a0467b8cd48\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74a8815656e6ae477cc0ff49935ee45\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74a8815656e6ae477cc0ff49935ee45\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01ee4962a5ed47a8c1f3b06dbeacfd46\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\01ee4962a5ed47a8c1f3b06dbeacfd46\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aa34306e16f3bbcf7d9f2c028e6d56b\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aa34306e16f3bbcf7d9f2c028e6d56b\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4104d6451bda1e06c2db9cec675b5d77\transformed\jetified-viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4104d6451bda1e06c2db9cec675b5d77\transformed\jetified-viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d75a5928c778e2e068179a18a2149edd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d75a5928c778e2e068179a18a2149edd\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e865ac42435c71c7d834ce4071e5cb7c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e865ac42435c71c7d834ce4071e5cb7c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f36302c7730400571c9add63713632\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12f36302c7730400571c9add63713632\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5910f0094b7e90a8cfd982b9edd9fb36\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5910f0094b7e90a8cfd982b9edd9fb36\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f08243ee29d0209319f5809d2cf35d9e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f08243ee29d0209319f5809d2cf35d9e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54c3b60611749c741cabf3a7a812dde4\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54c3b60611749c741cabf3a7a812dde4\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a0791a6c9fa28d46b03b636d3da8b9\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a0791a6c9fa28d46b03b636d3da8b9\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3b0e5bf1d9235d9a613f8c5d332c501\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f3b0e5bf1d9235d9a613f8c5d332c501\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e70112bbc84ddf19fc928e7dec52d465\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e70112bbc84ddf19fc928e7dec52d465\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-50
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-86
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-87
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-81
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-83
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-88
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-92
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-84
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-83
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:5-91
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-92
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:5-93
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:22-90
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
	android:enabled
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
	android:name
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c4a036db10ec7cd0d359d0598a3abc7\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c4a036db10ec7cd0d359d0598a3abc7\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
queries
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:10:5-18:15
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:10:5-18:15
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:15:5-17:15
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:15:5-17:15
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:8:5-12:15
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:8:5-12:15
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:10:5-39:15
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:10:5-39:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
activity#com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:configChanges
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
receiver#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
	android:enabled
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
meta-data#io.flutter.embedded_views_preview
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
	android:value
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:13:9-17:18
action#android.intent.action.VIEW
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
data
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
	android:scheme
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:21:9-65:20
	android:launchMode
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:24:13-44
	android:exported
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:23:13-36
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:22:13-109
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/${applicationId}/cancel+data:path:/${applicationId}/success+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}/authentication_return+data:pathPrefix:/${applicationId}/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/com.velvete.ly/cancel+data:path:/com.velvete.ly/success+data:pathPrefix:/com.velvete.ly+data:pathPrefix:/com.velvete.ly+data:pathPrefix:/com.velvete.ly/authentication_return+data:pathPrefix:/com.velvete.ly/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
category#android.intent.category.DEFAULT
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetActivity
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:66:9-69:77
	android:exported
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:68:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:69:13-74
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:67:13-101
activity#com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:70:9-74:58
	android:windowSoftInputMode
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:74:13-55
	android:exported
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:73:13-74
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae6a82fc91dfe43b0c72cf3c3219531b\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:71:13-110
package#com.facebook.katana
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:9-55
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:18-52
activity#com.facebook.FacebookActivity
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
	android:configChanges
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:22:13-96
	android:theme
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:23:13-63
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:21:13-57
activity#com.facebook.CustomTabMainActivity
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:9-71
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:19-68
activity#com.facebook.CustomTabActivity
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
	tools:node
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:28:13-31
	android:exported
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:27:13-36
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:26:13-58
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.${applicationId}+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.com.velvete.ly+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\05110ea20b3f7db9fad2865620f70385\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
activity#com.stripe.android.paymentsheet.PaymentSheetActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:9:13-80
activity#com.stripe.android.paymentsheet.PaymentOptionsActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:12:9-15:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:14:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:15:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:13:13-82
activity#com.stripe.android.customersheet.CustomerSheetActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:16:9-19:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:18:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:19:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:17:13-82
activity#com.stripe.android.paymentsheet.addresselement.AddressElementActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:20:9-23:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:22:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:21:13-97
activity#com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:24:9-27:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:27:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:25:13-118
activity#com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:28:9-31:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:31:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:29:13-105
activity#com.stripe.android.paymentsheet.ui.SepaMandateActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:32:9-35:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:35:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:33:13-82
activity#com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:36:9-39:68
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:38:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:37:13-94
activity#com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:40:9-42:69
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:42:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:41:13-121
activity#com.stripe.android.paymentelement.embedded.form.FormActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:43:9-45:69
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:45:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:44:13-88
activity#com.stripe.android.paymentelement.embedded.manage.ManageActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:46:9-48:69
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:48:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:47:13-92
activity#com.stripe.android.link.LinkActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:49:9-56:58
	android:label
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:54:13-48
	android:autoRemoveFromRecents
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:51:13-49
	android:windowSoftInputMode
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:56:13-55
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:53:13-37
	android:configChanges
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:52:13-115
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:55:13-55
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:50:13-64
activity#com.stripe.android.link.LinkForegroundActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:57:9-62:61
	android:launchMode
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:61:13-43
	android:autoRemoveFromRecents
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:59:13-49
	android:configChanges
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:60:13-115
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:62:13-58
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:58:13-74
activity#com.stripe.android.link.LinkRedirectHandlerActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:63:9-80:20
	android:launchMode
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:67:13-48
	android:autoRemoveFromRecents
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:65:13-49
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:66:13-36
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:68:13-58
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:64:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/${applicationId}+data:scheme:link-popup
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/com.velvete.ly+data:scheme:link-popup
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1323dd2cdfc659f5e9c34787409b8d6\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
activity#com.stripe.android.ui.core.cardscan.CardScanActivity
ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f850e4c971b259208e0a065d9bdf1eeb\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:9:13-80
package#com.android.chrome
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:9-54
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:18-51
activity#com.stripe.android.view.PaymentAuthWebViewActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:15:9-18:57
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:17:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:18:13-54
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:16:13-78
activity#com.stripe.android.view.PaymentRelayActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:19:9-22:61
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:22:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:20:13-72
activity#com.stripe.android.payments.StripeBrowserLauncherActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:28:9-32:61
	android:launchMode
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:31:13-44
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:29:13-85
activity#com.stripe.android.payments.StripeBrowserProxyReturnActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:33:9-50:20
	android:launchMode
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:36:13-44
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:37:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:34:13-88
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/${applicationId}+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/com.velvete.ly+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
activity#com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:51:9-54:57
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:53:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:54:13-54
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:52:13-114
activity#com.stripe.android.googlepaylauncher.GooglePayLauncherActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:55:9-58:66
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:57:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:58:13-63
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:56:13-90
activity#com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:59:9-62:66
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:61:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:62:13-63
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:60:13-103
activity#com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:63:9-66:68
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:65:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:66:13-65
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:64:13-107
activity#com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:67:9-70:61
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:69:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:70:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d049bede0c74f46f6325187c9a3899f0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:68:13-97
activity#com.stripe.android.stripe3ds2.views.ChallengeActivity
ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:8:9-11:54
	android:exported
		ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:11:13-51
	android:name
		ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13a52594bedbd674c7585af371c24fcc\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:9:13-81
intent#action:name:android.intent.action.VIEW+data:mimeType:*/*+data:scheme:*
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:11:9-17:18
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:host:pay+data:mimeType:*/*+data:scheme:upi
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:18:9-27:18
intent#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:28:9-30:18
action#android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:31:9-35:18
action#android.intent.action.SEND
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:21-62
intent#action:name:rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:36:9-38:18
action#rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:13-61
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:21-58
activity#com.razorpay.CheckoutActivity
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:42:9-50:20
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:45:13-37
	android:configChanges
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:44:13-83
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:46:13-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:43:13-57
intent-filter#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:47:13-49:29
provider#androidx.startup.InitializationProvider
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:52:9-60:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66a3e28d8d72eb263995f5e2d6fe14a9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66a3e28d8d72eb263995f5e2d6fe14a9\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:56:13-31
	android:authorities
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:54:13-68
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:55:13-37
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:53:13-67
meta-data#com.razorpay.RazorpayInitializer
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:57:13-59:52
	android:value
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:59:17-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:58:17-64
activity#com.razorpay.MagicXActivity
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:62:9-65:75
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:64:13-37
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:65:13-72
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:63:13-55
meta-data#com.razorpay.plugin.googlepay_all
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:67:9-69:58
	android:value
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:69:13-55
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f92a6422ab7639dd4a8f6af2c1c3fa87\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:68:13-61
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5478d5a9b7241377be4dc732788798a\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:5-79
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:5-88
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:5-82
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:5-92
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:22-89
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:5-83
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:22-80
provider#com.facebook.internal.FacebookInitProvider
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
	android:authorities
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:34:13-72
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:33:13-70
receiver#com.facebook.CurrentAccessTokenExpirationBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:39:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:38:13-86
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
action#com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:17-95
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:25-92
receiver#com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:46:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:45:13-118
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
action#com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:17-103
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\39f35a356b16a9f2f69d63bb6af96c4a\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:25-100
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c4a036db10ec7cd0d359d0598a3abc7\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3c4a036db10ec7cd0d359d0598a3abc7\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\69d31a6f75f9e28f99da890d10296a19\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fe13ad2fdb936d080b2f9b152c723c8d\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adae0248348ab1681e02bbc1d8302f29\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\96cfae243f0c1de092dec30d65eca9fb\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\531455f7a7436a5d904b361bb99430d5\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc2187caf1da4f016ec2316f397c1b27\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b0449a5ebb73d69ff576c8d7485f9ab3\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a25ed07a56cf8c699e5433427eb2b29e\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5cf3f7d970f00edc7b3adeeb67381eb\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a0791a6c9fa28d46b03b636d3da8b9\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24a0791a6c9fa28d46b03b636d3da8b9\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b020477bdb9653a2d217a1c1ccf01ff\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3593559ab525147ce7bebe0cc3ccb263\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ed4d7baffc8dbce41a087c297d2ccd6e\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784a1bc06f23e39dd257d9fc4dcaab1d\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\629647a9ef1e9657c8cbc8cc2c222213\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c157e045afac044a0a83773adfc6c0e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e70112bbc84ddf19fc928e7dec52d465\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e70112bbc84ddf19fc928e7dec52d465\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
meta-data#aia-compat-api-min-version
ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
	android:value
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
	android:name
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\732f69ec9bd76ae2cd9a314377618ede\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56bf51808dfa4b10779e01d49dee17eb\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
