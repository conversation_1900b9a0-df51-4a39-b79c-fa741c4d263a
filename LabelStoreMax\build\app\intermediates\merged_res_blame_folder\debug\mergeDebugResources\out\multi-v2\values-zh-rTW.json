{"logs": [{"outputFile": "com.velvete.ly.app-mergeDebugResources-108:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0e964f52d4e8d584d79488f5fbb8af38\\transformed\\jetified-ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,883,948,1021,1096,1164,1238,1306", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,878,943,1016,1091,1159,1233,1301,1417"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,505,506,507", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4138,4215,8546,8633,8794,9622,9696,13873,13951,14096,14161,14415,14488,14790,38941,39015,39083", "endColumns": "76,75,86,90,77,73,76,77,74,64,64,72,74,67,73,67,115", "endOffsets": "4210,4286,8628,8719,8867,9691,9768,13946,14021,14156,14221,14483,14558,14853,39010,39078,39194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2b020477bdb9653a2d217a1c1ccf01ff\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "7441", "endColumns": "102", "endOffsets": "7539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5c4f53641dc3d7e8f1b0d51e51a6bc58\\transformed\\jetified-foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "508,509", "startColumns": "4,4", "startOffsets": "39199,39282", "endColumns": "82,78", "endOffsets": "39277,39356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a02b55c0e8015c9916fce5af2c7d98d9\\transformed\\jetified-stripe-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "206,216,218,219,220,224,232,235,238,242,246,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16429,17144,17271,17331,17404,17657,18158,18338,18552,18836,19132,19390", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "16495,17196,17326,17399,17457,17714,18210,18400,18613,18891,19184,19448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3593559ab525147ce7bebe0cc3ccb263\\transformed\\core-1.13.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3203,3295,3394,3488,3582,3675,3768,14928", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3290,3389,3483,3577,3670,3763,3859,15024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adae0248348ab1681e02bbc1d8302f29\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6577,6678,6806,6921,7023,7130,7246,7346,7544,7654,7755,7884,7999,8101,8209,8265,8322", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "6673,6801,6916,7018,7125,7241,7341,7436,7649,7750,7879,7994,8096,8204,8260,8317,8391"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d049bede0c74f46f6325187c9a3899f0\\transformed\\jetified-payments-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,245,306,375,429,489,537,602,668,736,820,904,977,1046,1116,1187,1267,1346,1409,1485,1558,1633,1721,1791,1867,1937,2020,2085,2160,2233,2302,2371,2453,2513,2580,2672,2757,2820,2881,3207,3508,3573,3657,3737,3792,3868,3940,3998,4069,4142,4197,4267,4312,4382,4464,4521,4586,4653,4720,4764,4828,4905,4969,5012,5055,5110,5168,5226,5317,5409,5486,5546,5609,5668,5743,5806,5866,5928,5999,6057,6131,6202,6279,6328,6403,6448,6498,6554,6610,6671,6730,6791,6862,6919,6964", "endColumns": "59,67,61,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,74,87,69,75,69,82,64,74,72,68,68,81,59,66,91,84,62,60,325,300,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,70,76,48,74,44,49,55,55,60,58,60,70,56,44,61", "endOffsets": "110,178,240,301,370,424,484,532,597,663,731,815,899,972,1041,1111,1182,1262,1341,1404,1480,1553,1628,1716,1786,1862,1932,2015,2080,2155,2228,2297,2366,2448,2508,2575,2667,2752,2815,2876,3202,3503,3568,3652,3732,3787,3863,3935,3993,4064,4137,4192,4262,4307,4377,4459,4516,4581,4648,4715,4759,4823,4900,4964,5007,5050,5105,5163,5221,5312,5404,5481,5541,5604,5663,5738,5801,5861,5923,5994,6052,6126,6197,6274,6323,6398,6443,6493,6549,6605,6666,6725,6786,6857,6914,6959,7021"}, "to": {"startLines": "191,192,193,194,195,196,197,201,202,203,204,207,209,210,212,217,221,236,239,240,241,243,244,245,247,251,252,253,254,255,256,257,258,259,260,262,265,266,272,273,274,285,286,287,288,289,290,291,292,293,294,295,296,303,304,305,308,309,310,311,315,320,321,322,323,324,329,330,331,332,333,334,338,339,348,349,351,352,356,357,359,364,371,372,443,444,445,447,452,465,466,467,468,469,470,471,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15502,15562,15630,15692,15753,15822,15876,16122,16170,16235,16301,16500,16655,16739,16876,17201,17462,18405,18618,18697,18760,18896,18969,19044,19189,19453,19529,19599,19682,19747,19822,19895,19964,20033,20115,20248,20451,20543,21109,21172,21233,22260,22561,22626,22710,22790,22845,22921,22993,23051,23122,23195,23250,23707,23752,23822,24049,24106,24171,24238,24563,24870,24934,25011,25075,25118,25382,25437,25495,25553,25644,25736,25979,26039,26674,26733,26876,26939,27205,27267,27398,27822,28249,28320,34195,34244,34319,34444,34838,36095,36151,36212,36271,36332,36403,36460,37552", "endColumns": "59,67,61,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,74,87,69,75,69,82,64,74,72,68,68,81,59,66,91,84,62,60,325,300,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,70,76,48,74,44,49,55,55,60,58,60,70,56,44,61", "endOffsets": "15557,15625,15687,15748,15817,15871,15931,16165,16230,16296,16364,16579,16734,16807,16940,17266,17528,18480,18692,18755,18831,18964,19039,19127,19254,19524,19594,19677,19742,19817,19890,19959,20028,20110,20170,20310,20538,20623,21167,21228,21554,22556,22621,22705,22785,22840,22916,22988,23046,23117,23190,23245,23315,23747,23817,23899,24101,24166,24233,24300,24602,24929,25006,25070,25113,25156,25432,25490,25548,25639,25731,25808,26034,26097,26728,26803,26934,26994,27262,27333,27451,27891,28315,28392,34239,34314,34359,34489,34889,36146,36207,36266,36327,36398,36455,36500,37609"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1323dd2cdfc659f5e9c34787409b8d6\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,180,241,310,381,454,527,605,674,745,817,896,947,1019,1078,1196,1318,1413,1498,1580,1664,1750,1854,1942,2028,2147,2232,2331,2499,2666,2754,2844,2946,3025,3082,3141,3215,3293,3370,3436,3514,3591,3676,3797,3861,3923,3984,4074,4144,4217,4276,4345,4413,4476,4564,4645,4736,4817,4882,4953,5017,5088,5144,5227,5303,5387,5487,5545,5622,5706,5807,5874,5937,6017,6067,6118,6176,6245,6363,6528,6711,6790,6850,6915,6974,7054,7139,7212,7280,7345,7414,7477,7552,7630,7714,7779,7842,7892,8005,8070,8123,8191,8253,8331,8397,8482,8568,8646,8717,8784,8847,8907", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,73,77,76,65,77,76,84,120,63,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,99,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,79,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "109,175,236,305,376,449,522,600,669,740,812,891,942,1014,1073,1191,1313,1408,1493,1575,1659,1745,1849,1937,2023,2142,2227,2326,2494,2661,2749,2839,2941,3020,3077,3136,3210,3288,3365,3431,3509,3586,3671,3792,3856,3918,3979,4069,4139,4212,4271,4340,4408,4471,4559,4640,4731,4812,4877,4948,5012,5083,5139,5222,5298,5382,5482,5540,5617,5701,5802,5869,5932,6012,6062,6113,6171,6240,6358,6523,6706,6785,6845,6910,6969,7049,7134,7207,7275,7340,7409,7472,7547,7625,7709,7774,7837,7887,8000,8065,8118,8186,8248,8326,8392,8477,8563,8641,8712,8779,8842,8902,8996"}, "to": {"startLines": "198,199,200,270,282,283,284,301,314,316,317,347,365,367,370,374,375,376,379,381,383,384,385,386,387,388,389,390,391,392,393,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,442,446,456,457,458,459,460,461,462,463,464,473,474,475,476,477,478,479,480,481,482,483,484,485,486,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15936,15995,16061,20928,22043,22114,22187,23565,24494,24607,24678,26595,27896,28001,28190,28455,28573,28695,28917,29092,29251,29335,29421,29525,29613,29699,29818,29903,30002,30170,30337,30502,30592,30694,30773,30830,30889,30963,31041,31118,31184,31262,31339,31424,31545,31609,31671,31732,31822,31892,31965,32024,32093,32161,32224,32721,32802,32893,32974,33039,33110,33174,33245,33301,33384,33460,33544,33644,33702,33779,33863,33964,34132,34364,35262,35312,35363,35421,35490,35608,35773,35956,36035,36560,36625,36684,36764,36849,36922,36990,37055,37124,37187,37262,37340,37424,37489,37614,37664,37777,37842,37895,37963,38025,38103,38169,38254,38340,38418,38489,38556,38619,38679", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,73,77,76,65,77,76,84,120,63,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,99,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,79,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "15990,16056,16117,20992,22109,22182,22255,23638,24558,24673,24745,26669,27942,28068,28244,28568,28690,28785,28997,29169,29330,29416,29520,29608,29694,29813,29898,29997,30165,30332,30420,30587,30689,30768,30825,30884,30958,31036,31113,31179,31257,31334,31419,31540,31604,31666,31727,31817,31887,31960,32019,32088,32156,32219,32307,32797,32888,32969,33034,33105,33169,33240,33296,33379,33455,33539,33639,33697,33774,33858,33959,34026,34190,34439,35307,35358,35416,35485,35603,35768,35951,36030,36090,36620,36679,36759,36844,36917,36985,37050,37119,37182,37257,37335,37419,37484,37547,37659,37772,37837,37890,37958,38020,38098,38164,38249,38335,38413,38484,38551,38614,38674,38768"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aacdcb9fcbca67a896797d8f06149192\\transformed\\appcompat-1.7.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,14563", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,14637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13a52594bedbd674c7585af371c24fcc\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,467", "endColumns": "74,83,64,65,59,61,60", "endOffsets": "125,209,274,340,400,462,523"}, "to": {"startLines": "184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "15029,15104,15188,15253,15319,15379,15441", "endColumns": "74,83,64,65,59,61,60", "endOffsets": "15099,15183,15248,15314,15374,15436,15497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9fbae9977177ee1e6c51272f147efa70\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,67", "endOffsets": "262,330"}, "to": {"startLines": "106,512", "startColumns": "4,4", "startOffsets": "9501,39516", "endColumns": "60,71", "endOffsets": "9557,39583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b04a7b60dccd9124e74d23c94f4b7089\\transformed\\jetified-facebook-login-18.0.3\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,287,398,507,587,669,745,830,915,1022,1126,1212,1298,1392,1501,1579,1658,1781,1874,1969,2075,2169", "endColumns": "130,100,110,108,79,81,75,84,84,106,103,85,85,93,108,77,78,122,92,94,105,93,99", "endOffsets": "181,282,393,502,582,664,740,825,910,1017,1121,1207,1293,1387,1496,1574,1653,1776,1869,1964,2070,2164,2264"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4363,4494,4595,4706,4815,4895,4977,5053,5138,5223,5330,5434,5520,5606,5700,5809,5887,5966,6089,6182,6277,6383,6477", "endColumns": "130,100,110,108,79,81,75,84,84,106,103,85,85,93,108,77,78,122,92,94,105,93,99", "endOffsets": "4489,4590,4701,4810,4890,4972,5048,5133,5218,5325,5429,5515,5601,5695,5804,5882,5961,6084,6177,6272,6378,6472,6572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\98e987c5461daad3161f262e3ab33d58\\transformed\\material-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,912,974,1052,1112,1172,1250,1311,1369,1425,1485,1543,1597,1682,1738,1796,1850,1915,2007,2081,2153,2235,2309,2386,2506,2569,2632,2731,2808,2882,2932,2983,3049,3112,3180,3251,3322,3383,3454,3521,3583,3670,3749,3814,3897,3982,4056,4120,4196,4244,4317,4381,4457,4535,4597,4661,4724,4790,4870,4948,5024,5103,5157,5212,5281,5356,5429", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "242,306,368,435,505,582,676,783,856,907,969,1047,1107,1167,1245,1306,1364,1420,1480,1538,1592,1677,1733,1791,1845,1910,2002,2076,2148,2230,2304,2381,2501,2564,2627,2726,2803,2877,2927,2978,3044,3107,3175,3246,3317,3378,3449,3516,3578,3665,3744,3809,3892,3977,4051,4115,4191,4239,4312,4376,4452,4530,4592,4656,4719,4785,4865,4943,5019,5098,5152,5207,5276,5351,5424,5494"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2863,2927,2989,3056,3126,3864,3958,4065,8872,8923,9137,9562,9773,9833,9911,9972,10030,10086,10146,10204,10258,10343,10399,10457,10511,10576,10668,10742,10814,10896,10970,11047,11167,11230,11293,11392,11469,11543,11593,11644,11710,11773,11841,11912,11983,12044,12115,12182,12244,12331,12410,12475,12558,12643,12717,12781,12857,12905,12978,13042,13118,13196,13258,13322,13385,13451,13531,13609,13685,13764,13818,14226,14642,14717,14858", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "292,2922,2984,3051,3121,3198,3953,4060,4133,8918,8980,9210,9617,9828,9906,9967,10025,10081,10141,10199,10253,10338,10394,10452,10506,10571,10663,10737,10809,10891,10965,11042,11162,11225,11288,11387,11464,11538,11588,11639,11705,11768,11836,11907,11978,12039,12110,12177,12239,12326,12405,12470,12553,12638,12712,12776,12852,12900,12973,13037,13113,13191,13253,13317,13380,13446,13526,13604,13680,13759,13813,13868,14290,14712,14785,14923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\10278739ca06d9f1b8a212e0d6ee3d93\\transformed\\jetified-material3-1.0.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,197", "endColumns": "71,69,70", "endOffsets": "122,192,263"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4291,8724,9066", "endColumns": "71,69,70", "endOffsets": "4358,8789,9132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c5fe46e059e11d85049d645a77ee33cc\\transformed\\browser-1.8.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "8462,9215,9307,9408", "endColumns": "83,91,100,92", "endOffsets": "8541,9302,9403,9496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ef8995c4c5b3cc98db66b1d0d7247bca\\transformed\\preference-1.2.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,689", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "166,247,317,437,605,684,760"}, "to": {"startLines": "92,100,171,175,504,510,511", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8396,8985,14026,14295,38773,39361,39440", "endColumns": "65,80,69,119,167,78,75", "endOffsets": "8457,9061,14091,14410,38936,39435,39511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ce085bb0fffc00796fbc2583b1fd195a\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,573,633,701,765,826,884,950,1012,1077,1135,1202,1261,1333,1406,1471,1542,1614,1677,1722,1768,1830,1892,1945,2007,2079,2146,2216", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,568,628,696,760,821,879,945,1007,1072,1130,1197,1256,1328,1401,1466,1537,1609,1672,1717,1763,1825,1887,1940,2002,2074,2141,2211,2280"}, "to": {"startLines": "205,208,211,213,214,215,222,223,225,226,227,228,229,230,231,233,234,237,248,249,261,263,264,298,299,313,325,326,328,335,336,345,346,354,355", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16369,16584,16812,16945,17018,17083,17533,17596,17719,17779,17847,17911,17972,18030,18096,18215,18280,18485,19259,19318,20175,20315,20380,23376,23448,24449,25161,25207,25320,25813,25866,26456,26528,27066,27136", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,71,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "16424,16650,16871,17013,17078,17139,17591,17652,17774,17842,17906,17967,18025,18091,18153,18275,18333,18547,19313,19385,20243,20375,20446,23443,23506,24489,25202,25264,25377,25861,25923,26523,26590,27131,27200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f850e4c971b259208e0a065d9bdf1eeb\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,279,355,467,511,569,644,708,824,879,951,1007,1061,1125,1190,1270,1414,1474,1534,1585,1636,1702,1786,1863,1918,1990,2058,2125,2185,2260,2413,2479,2551,2605,2663,2722,2780,2841,2907,2997,3074,3151,3242,3326,3396,3475,3560,3661,3770,3862,3956,4005,4241,4316,4373", "endColumns": "88,134,75,111,43,57,74,63,115,54,71,55,53,63,64,79,143,59,59,50,50,65,83,76,54,71,67,66,59,74,152,65,71,53,57,58,57,60,65,89,76,76,90,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "139,274,350,462,506,564,639,703,819,874,946,1002,1056,1120,1185,1265,1409,1469,1529,1580,1631,1697,1781,1858,1913,1985,2053,2120,2180,2255,2408,2474,2546,2600,2658,2717,2775,2836,2902,2992,3069,3146,3237,3321,3391,3470,3555,3656,3765,3857,3951,4000,4236,4311,4368,4423"}, "to": {"startLines": "267,268,269,271,275,276,277,278,279,280,281,297,300,302,306,307,312,318,319,327,337,340,341,342,343,344,350,353,358,360,361,362,363,366,368,369,373,377,378,380,382,394,419,420,421,422,423,441,448,449,450,451,453,454,455,472", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20628,20717,20852,20997,21559,21603,21661,21736,21800,21916,21971,23320,23511,23643,23904,23969,24305,24750,24810,25269,25928,26102,26168,26252,26329,26384,26808,26999,27338,27456,27531,27684,27750,27947,28073,28131,28397,28790,28851,29002,29174,30425,32312,32403,32487,32557,32636,34031,34494,34603,34695,34789,34894,35130,35205,36505", "endColumns": "88,134,75,111,43,57,74,63,115,54,71,55,53,63,64,79,143,59,59,50,50,65,83,76,54,71,67,66,59,74,152,65,71,53,57,58,57,60,65,89,76,76,90,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "20712,20847,20923,21104,21598,21656,21731,21795,21911,21966,22038,23371,23560,23702,23964,24044,24444,24805,24865,25315,25974,26163,26247,26324,26379,26451,26871,27061,27393,27526,27679,27745,27817,27996,28126,28185,28450,28846,28912,29087,29246,30497,32398,32482,32552,32631,32716,34127,34598,34690,34784,34833,35125,35200,35257,36555"}}]}]}